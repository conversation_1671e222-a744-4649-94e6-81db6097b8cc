# 刺绣管理系统最终架构设计 v1.0

**创建时间：** 2025-06-27 09:49:33 +08:00  
**基于方案：** 渐进式混合架构（方案C）  
**架构师：** AI系统架构师

## 1. 系统总体架构

### 1.1 架构概览
```
┌─────────────────────────────────────────────────────────────┐
│                    刺绣管理系统架构                          │
├─────────────────────────────────────────────────────────────┤
│  前端层 (Vue 3 + TypeScript + Ant Design Vue)              │
│  ├── 用户管理模块    ├── 订单管理模块    ├── 生产计划模块    │
│  ├── 库存管理模块    ├── 工资管理模块    ├── 数字空间模块    │
├─────────────────────────────────────────────────────────────┤
│  API网关层 (Express.js + TypeScript)                       │
│  ├── 认证中间件      ├── 权限中间件      ├── 日志中间件      │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (模块化服务)                                    │
│  ├── 认证服务        ├── 用户服务        ├── 企业服务        │
│  ├── 权限服务        ├── 部门服务        ├── 业务模块服务    │
├─────────────────────────────────────────────────────────────┤
│  数据访问层 (Sequelize ORM)                                │
├─────────────────────────────────────────────────────────────┤
│  数据存储层 (PostgreSQL)                                   │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 核心设计原则
- **模块化设计**：清晰的模块边界，支持独立开发和测试
- **分层架构**：职责分离，易于维护和扩展
- **安全优先**：多层安全防护，数据加密存储
- **性能优化**：合理的缓存策略和数据库优化
- **可扩展性**：支持水平扩展和功能扩展

## 2. 数据库设计

### 2.1 核心表结构
```sql
-- 企业表
CREATE TABLE enterprises (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    logo_url VARCHAR(255),
    description TEXT,
    status INTEGER DEFAULT 1, -- 1:正常 0:禁用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 部门表（支持树形结构）
CREATE TABLE departments (
    id SERIAL PRIMARY KEY,
    enterprise_id INTEGER REFERENCES enterprises(id),
    name VARCHAR(100) NOT NULL,
    parent_id INTEGER REFERENCES departments(id),
    description TEXT,
    level INTEGER DEFAULT 1,
    path VARCHAR(500), -- 层级路径，如：1/2/3
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 角色表
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    enterprise_id INTEGER REFERENCES enterprises(id),
    name VARCHAR(50) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE, -- 系统预定义角色
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 权限表
CREATE TABLE permissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    module VARCHAR(50) NOT NULL, -- 所属模块
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    enterprise_id INTEGER REFERENCES enterprises(id),
    department_id INTEGER REFERENCES departments(id),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    real_name VARCHAR(50) NOT NULL,
    avatar_url VARCHAR(255),
    status INTEGER DEFAULT 1, -- 1:正常 0:禁用
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE user_roles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    role_id INTEGER REFERENCES roles(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 角色权限关联表
CREATE TABLE role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER REFERENCES roles(id),
    permission_id INTEGER REFERENCES permissions(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 索引设计
```sql
-- 性能优化索引
CREATE INDEX idx_users_enterprise_id ON users(enterprise_id);
CREATE INDEX idx_users_department_id ON users(department_id);
CREATE INDEX idx_departments_enterprise_id ON departments(enterprise_id);
CREATE INDEX idx_departments_parent_id ON departments(parent_id);
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
```

## 3. API设计规范

### 3.1 RESTful API设计
```
基础路径：/api/v1

认证相关：
POST   /auth/login          # 用户登录
POST   /auth/logout         # 用户登出
POST   /auth/refresh        # 刷新token
GET    /auth/profile        # 获取当前用户信息

企业管理：
GET    /enterprises         # 获取企业列表（超级管理员）
POST   /enterprises         # 创建企业（超级管理员）
GET    /enterprises/:id     # 获取企业详情
PUT    /enterprises/:id     # 更新企业信息
DELETE /enterprises/:id     # 删除企业（超级管理员）

用户管理：
GET    /users               # 获取用户列表
POST   /users               # 创建用户
GET    /users/:id           # 获取用户详情
PUT    /users/:id           # 更新用户信息
DELETE /users/:id           # 删除用户
PUT    /users/:id/status    # 更新用户状态

部门管理：
GET    /departments         # 获取部门树
POST   /departments         # 创建部门
GET    /departments/:id     # 获取部门详情
PUT    /departments/:id     # 更新部门信息
DELETE /departments/:id     # 删除部门

角色权限：
GET    /roles               # 获取角色列表
POST   /roles               # 创建角色
PUT    /roles/:id           # 更新角色
DELETE /roles/:id           # 删除角色
GET    /permissions         # 获取权限列表
PUT    /roles/:id/permissions # 分配权限
```

### 3.2 响应格式标准
```typescript
// 成功响应
interface ApiResponse<T> {
  code: number;        // 状态码：200成功
  message: string;     // 响应消息
  data: T;            // 响应数据
  timestamp: string;   // 时间戳
}

// 错误响应
interface ApiError {
  code: number;        // 错误码：400客户端错误，500服务器错误
  message: string;     // 错误消息
  details?: any;       // 错误详情
  timestamp: string;   // 时间戳
}

// 分页响应
interface PageResponse<T> {
  list: T[];          // 数据列表
  total: number;      // 总数
  page: number;       // 当前页
  pageSize: number;   // 页大小
}
```

## 4. 前端架构设计

### 4.1 目录结构
```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 公共组件
│   │   ├── Layout/        # 布局组件
│   │   ├── Form/          # 表单组件
│   │   └── Table/         # 表格组件
│   ├── modules/           # 业务模块
│   │   ├── auth/          # 认证模块
│   │   ├── user/          # 用户管理
│   │   ├── enterprise/    # 企业管理
│   │   ├── department/    # 部门管理
│   │   └── permission/    # 权限管理
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── api/               # API接口
│   ├── utils/             # 工具函数
│   ├── types/             # TypeScript类型
│   └── assets/            # 静态资源
├── package.json
├── vite.config.ts
└── tsconfig.json
```

### 4.2 权限控制设计
```typescript
// 路由权限守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore();
  const requiredPermissions = to.meta.permissions as string[];
  
  if (requiredPermissions && !userStore.hasPermissions(requiredPermissions)) {
    next('/403');
  } else {
    next();
  }
});

// 组件级权限控制
<template>
  <div v-if="hasPermission('user:create')">
    <a-button @click="createUser">创建用户</a-button>
  </div>
</template>
```

## 5. 后端架构设计

### 5.1 目录结构
```
backend/
├── src/
│   ├── modules/           # 业务模块
│   │   ├── auth/          # 认证模块
│   │   ├── user/          # 用户管理
│   │   ├── enterprise/    # 企业管理
│   │   └── permission/    # 权限管理
│   ├── shared/            # 共享模块
│   │   ├── database/      # 数据库配置
│   │   ├── middleware/    # 中间件
│   │   ├── utils/         # 工具函数
│   │   └── types/         # 类型定义
│   ├── config/            # 配置文件
│   └── app.ts             # 应用入口
├── package.json
├── tsconfig.json
└── .env.example
```

### 5.2 中间件设计
```typescript
// JWT认证中间件
export const authMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  if (!token) {
    return res.status(401).json({ message: '未提供认证令牌' });
  }
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ message: '无效的认证令牌' });
  }
};

// 权限验证中间件
export const permissionMiddleware = (permissions: string[]) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    const userPermissions = await getUserPermissions(req.user.id);
    const hasPermission = permissions.every(p => userPermissions.includes(p));
    
    if (!hasPermission) {
      return res.status(403).json({ message: '权限不足' });
    }
    
    next();
  };
};
```

## 6. 安全设计

### 6.1 认证安全
- JWT令牌机制，支持刷新令牌
- 密码使用bcrypt加密存储
- 登录失败次数限制
- 会话超时自动登出

### 6.2 权限安全
- 前后端双重权限验证
- 基于角色的访问控制(RBAC)
- 细粒度权限控制到按钮级别
- 数据行级权限控制

### 6.3 数据安全
- 参数化查询防止SQL注入
- 输入验证和数据清洗
- 敏感数据加密存储
- 审计日志记录

## 7. 性能优化

### 7.1 前端优化
- 路由懒加载
- 组件按需加载
- 图片懒加载
- 缓存策略

### 7.2 后端优化
- 数据库连接池
- 查询优化和索引
- Redis缓存
- 接口限流

### 7.3 数据库优化
- 合理的索引设计
- 查询优化
- 分页查询
- 连接池配置

这个架构设计为系统的实施提供了完整的技术蓝图，支持快速开发和未来扩展。
