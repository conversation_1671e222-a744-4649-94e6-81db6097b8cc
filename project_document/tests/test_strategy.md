# 刺绣管理系统测试策略

**创建时间：** 2025-06-27 09:49:33 +08:00  
**测试目标：** 确保系统质量、安全性和性能

## 1. 测试策略概览

### 1.1 测试金字塔
```
        E2E测试 (10%)
       ┌─────────────┐
      │  用户流程测试  │
     └─────────────────┘
    
    集成测试 (20%)
   ┌─────────────────────┐
  │   API接口测试        │
  │   数据库集成测试      │
 └─────────────────────────┘

单元测试 (70%)
┌─────────────────────────────┐
│  组件测试  │  服务测试       │
│  工具函数  │  业务逻辑测试   │
└─────────────────────────────┘
```

### 1.2 测试覆盖率目标
- **单元测试覆盖率：** ≥80%
- **API接口覆盖率：** 100%
- **关键业务流程覆盖率：** 100%
- **权限控制测试覆盖率：** 100%

## 2. 前端测试策略

### 2.1 单元测试 (Vitest)
**测试范围：**
- Vue组件单元测试
- 工具函数测试
- 状态管理(Pinia)测试
- 路由配置测试

**测试示例：**
```typescript
// 组件测试示例
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import UserForm from '@/components/UserForm.vue'

describe('UserForm', () => {
  it('should render form fields correctly', () => {
    const wrapper = mount(UserForm)
    expect(wrapper.find('[data-testid="username"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="email"]').exists()).toBe(true)
  })

  it('should validate required fields', async () => {
    const wrapper = mount(UserForm)
    await wrapper.find('form').trigger('submit')
    expect(wrapper.text()).toContain('用户名不能为空')
  })
})
```

### 2.2 E2E测试 (Playwright)
**测试场景：**
- 用户登录流程
- 权限控制验证
- 核心业务流程
- 跨浏览器兼容性

**测试示例：**
```typescript
// E2E测试示例
import { test, expect } from '@playwright/test'

test('用户登录流程', async ({ page }) => {
  await page.goto('/login')
  
  // 填写登录表单
  await page.fill('[data-testid="username"]', 'admin')
  await page.fill('[data-testid="password"]', 'password123')
  await page.click('[data-testid="login-btn"]')
  
  // 验证登录成功
  await expect(page).toHaveURL('/dashboard')
  await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible()
})

test('权限控制测试', async ({ page }) => {
  // 以普通用户身份登录
  await loginAsUser(page, 'user', 'password')
  
  // 验证无权限页面不可访问
  await page.goto('/admin/users')
  await expect(page).toHaveURL('/403')
})
```

## 3. 后端测试策略

### 3.1 单元测试 (Jest)
**测试范围：**
- 服务层业务逻辑
- 工具函数
- 中间件功能
- 数据验证逻辑

**测试示例：**
```typescript
// 服务层测试示例
import { UserService } from '@/modules/user/user.service'
import { mockUserRepository } from '@/test/mocks'

describe('UserService', () => {
  let userService: UserService

  beforeEach(() => {
    userService = new UserService(mockUserRepository)
  })

  it('should create user successfully', async () => {
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    }

    const result = await userService.createUser(userData)
    
    expect(result.username).toBe(userData.username)
    expect(result.password).toBeUndefined() // 密码不应返回
  })

  it('should throw error for duplicate username', async () => {
    mockUserRepository.findByUsername.mockResolvedValue({ id: 1 })
    
    await expect(userService.createUser({
      username: 'existing',
      email: '<EMAIL>',
      password: 'password123'
    })).rejects.toThrow('用户名已存在')
  })
})
```

### 3.2 API集成测试 (Supertest)
**测试范围：**
- RESTful API接口
- 认证和授权
- 数据验证
- 错误处理

**测试示例：**
```typescript
// API测试示例
import request from 'supertest'
import { app } from '@/app'

describe('User API', () => {
  let authToken: string

  beforeAll(async () => {
    // 获取认证令牌
    const response = await request(app)
      .post('/api/v1/auth/login')
      .send({ username: 'admin', password: 'password123' })
    
    authToken = response.body.data.token
  })

  it('GET /api/v1/users should return user list', async () => {
    const response = await request(app)
      .get('/api/v1/users')
      .set('Authorization', `Bearer ${authToken}`)
      .expect(200)

    expect(response.body.code).toBe(200)
    expect(Array.isArray(response.body.data.list)).toBe(true)
  })

  it('POST /api/v1/users should create user', async () => {
    const userData = {
      username: 'newuser',
      email: '<EMAIL>',
      password: 'password123',
      realName: '新用户'
    }

    const response = await request(app)
      .post('/api/v1/users')
      .set('Authorization', `Bearer ${authToken}`)
      .send(userData)
      .expect(201)

    expect(response.body.data.username).toBe(userData.username)
  })

  it('should return 401 without auth token', async () => {
    await request(app)
      .get('/api/v1/users')
      .expect(401)
  })
})
```

## 4. 数据库测试策略

### 4.1 数据库集成测试
**测试范围：**
- 数据模型关联关系
- 数据库约束验证
- 复杂查询逻辑
- 事务处理

**测试示例：**
```typescript
// 数据库测试示例
import { sequelize } from '@/shared/database'
import { User, Department, Enterprise } from '@/models'

describe('Database Relations', () => {
  beforeEach(async () => {
    await sequelize.sync({ force: true })
  })

  it('should create user with department relation', async () => {
    const enterprise = await Enterprise.create({
      name: '测试企业',
      code: 'TEST001'
    })

    const department = await Department.create({
      name: '技术部',
      enterpriseId: enterprise.id
    })

    const user = await User.create({
      username: 'testuser',
      email: '<EMAIL>',
      passwordHash: 'hashedpassword',
      realName: '测试用户',
      enterpriseId: enterprise.id,
      departmentId: department.id
    })

    const userWithDept = await User.findByPk(user.id, {
      include: [Department, Enterprise]
    })

    expect(userWithDept.Department.name).toBe('技术部')
    expect(userWithDept.Enterprise.name).toBe('测试企业')
  })
})
```

## 5. 安全测试策略

### 5.1 认证授权测试
**测试场景：**
- JWT令牌验证
- 权限边界测试
- 会话管理测试
- 密码安全测试

### 5.2 输入验证测试
**测试场景：**
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护
- 文件上传安全

### 5.3 安全测试示例
```typescript
// 安全测试示例
describe('Security Tests', () => {
  it('should prevent SQL injection', async () => {
    const maliciousInput = "'; DROP TABLE users; --"
    
    const response = await request(app)
      .get(`/api/v1/users?search=${maliciousInput}`)
      .set('Authorization', `Bearer ${authToken}`)
    
    // 应该正常返回，不会执行恶意SQL
    expect(response.status).toBe(200)
  })

  it('should sanitize XSS input', async () => {
    const xssInput = '<script>alert("xss")</script>'
    
    const response = await request(app)
      .post('/api/v1/users')
      .set('Authorization', `Bearer ${authToken}`)
      .send({
        username: 'testuser',
        realName: xssInput,
        email: '<EMAIL>',
        password: 'password123'
      })
    
    expect(response.body.data.realName).not.toContain('<script>')
  })
})
```

## 6. 性能测试策略

### 6.1 负载测试
**测试工具：** Artillery.js
**测试场景：**
- API接口并发测试
- 数据库连接池测试
- 内存使用监控

### 6.2 性能测试示例
```yaml
# artillery配置示例
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
    - duration: 120
      arrivalRate: 50

scenarios:
  - name: "用户登录测试"
    flow:
      - post:
          url: "/api/v1/auth/login"
          json:
            username: "testuser"
            password: "password123"
      - get:
          url: "/api/v1/users"
          headers:
            Authorization: "Bearer {{ token }}"
```

## 7. 测试环境配置

### 7.1 测试数据库
- 使用独立的测试数据库
- 每次测试前重置数据
- 使用工厂模式生成测试数据

### 7.2 CI/CD集成
```yaml
# GitHub Actions示例
name: Test
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: |
          cd frontend && npm ci
          cd ../backend && npm ci
          
      - name: Run tests
        run: |
          cd frontend && npm run test:unit
          cd ../backend && npm run test
          
      - name: E2E tests
        run: |
          npm run test:e2e
```

## 8. 测试报告和监控

### 8.1 测试报告
- 覆盖率报告生成
- 测试结果可视化
- 性能测试报告

### 8.2 持续监控
- 测试执行时间监控
- 测试成功率统计
- 代码质量趋势分析

这个测试策略确保了系统的质量、安全性和性能，支持持续集成和持续部署。
