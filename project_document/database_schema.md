# 数据库表结构说明

## 表前缀配置

本项目使用 `em_` 作为数据库表前缀，其中 `em` 代表 `Embroidery Management`（刺绣管理）。

### 配置方式

在 `backend/.env` 文件中配置：
```bash
DB_TABLE_PREFIX=em_
```

### 数据库表列表

| 表名 | 说明 | 模型文件 |
|------|------|----------|
| `em_enterprises` | 企业表 | Enterprise.ts |
| `em_departments` | 部门表 | Department.ts |
| `em_users` | 用户表 | User.ts |
| `em_roles` | 角色表 | Role.ts |
| `em_permissions` | 权限表 | Permission.ts |
| `em_user_roles` | 用户角色关联表 | UserRole.ts |
| `em_role_permissions` | 角色权限关联表 | RolePermission.ts |

### 表结构详情

#### 1. em_enterprises (企业表)
```sql
CREATE TABLE em_enterprises (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '企业名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '企业编码',
    logo_url VARCHAR(255) COMMENT '企业Logo URL',
    description TEXT COMMENT '企业描述',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. em_departments (部门表)
```sql
CREATE TABLE em_departments (
    id SERIAL PRIMARY KEY,
    enterprise_id INTEGER NOT NULL REFERENCES em_enterprises(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL COMMENT '部门名称',
    parent_id INTEGER REFERENCES em_departments(id) ON DELETE SET NULL,
    description TEXT COMMENT '部门描述',
    level INTEGER NOT NULL DEFAULT 1 COMMENT '部门层级',
    path VARCHAR(500) NOT NULL COMMENT '层级路径',
    sort_order INTEGER NOT NULL DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. em_users (用户表)
```sql
CREATE TABLE em_users (
    id SERIAL PRIMARY KEY,
    enterprise_id INTEGER NOT NULL REFERENCES em_enterprises(id) ON DELETE CASCADE,
    department_id INTEGER REFERENCES em_departments(id) ON DELETE SET NULL,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    status INTEGER NOT NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
    last_login_at TIMESTAMP COMMENT '最后登录时间',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. em_roles (角色表)
```sql
CREATE TABLE em_roles (
    id SERIAL PRIMARY KEY,
    enterprise_id INTEGER REFERENCES em_enterprises(id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    code VARCHAR(50) NOT NULL COMMENT '角色编码',
    description TEXT COMMENT '角色描述',
    is_system BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为系统预定义角色',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(enterprise_id, code)
);
```

#### 5. em_permissions (权限表)
```sql
CREATE TABLE em_permissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '权限名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '权限编码',
    module VARCHAR(50) NOT NULL COMMENT '所属模块',
    description TEXT COMMENT '权限描述',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 6. em_user_roles (用户角色关联表)
```sql
CREATE TABLE em_user_roles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES em_users(id) ON DELETE CASCADE,
    role_id INTEGER NOT NULL REFERENCES em_roles(id) ON DELETE CASCADE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);
```

#### 7. em_role_permissions (角色权限关联表)
```sql
CREATE TABLE em_role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER NOT NULL REFERENCES em_roles(id) ON DELETE CASCADE,
    permission_id INTEGER NOT NULL REFERENCES em_permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, permission_id)
);
```

### 索引说明

每个表都包含以下索引：
- 主键索引
- 外键索引
- 唯一约束索引
- 查询优化索引（如状态字段、创建时间等）

### 数据完整性

1. **外键约束**：确保数据关联的完整性
2. **唯一约束**：防止重复数据
3. **非空约束**：确保必要字段不为空
4. **检查约束**：验证数据格式和范围

### 权限体系设计

系统采用基于角色的访问控制（RBAC）模型：

1. **用户（Users）**：系统的操作主体
2. **角色（Roles）**：权限的集合
3. **权限（Permissions）**：具体的操作权限
4. **企业（Enterprises）**：数据隔离的边界
5. **部门（Departments）**：组织结构的体现

### 三级权限体系

1. **超级管理员**：系统级权限，可管理所有企业
2. **企业管理员**：企业级权限，可管理本企业所有资源
3. **普通用户**：基于角色的功能权限

### 数据隔离

- 企业级数据隔离：用户只能访问所属企业的数据
- 部门级数据隔离：可根据需要实现部门级数据访问控制
- 角色级权限控制：基于角色分配的功能权限
