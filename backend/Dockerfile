# 后端Dockerfile
# {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: 主目录package.json, 创建后端Docker配置; Principle_Applied: 容器化部署;}}

# 使用官方Node.js 18 Alpine镜像
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    curl \
    postgresql-client \
    tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 开发阶段
FROM base AS development
RUN npm ci
COPY . .
EXPOSE 3000
CMD ["npm", "run", "dev"]

# 构建阶段
FROM base AS build
RUN npm ci
COPY . .
RUN npm run build

# 生产阶段
FROM base AS production

# 创建非root用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# 复制构建产物
COPY --from=build --chown=nodejs:nodejs /app/dist ./dist
COPY --from=build --chown=nodejs:nodejs /app/package*.json ./

# 创建必要的目录
RUN mkdir -p logs uploads && \
    chown -R nodejs:nodejs logs uploads

# 切换到非root用户
USER nodejs

# 暴露端口
EXPOSE 3002

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3002/health || exit 1

# 启动应用
CMD ["npm", "start"]
