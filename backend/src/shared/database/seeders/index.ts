/**
 * 数据库种子数据
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-002 数据库初始化, 创建种子数据; Principle_Applied: 初始数据管理;}}
 */

import { logger } from '@/shared/utils/logger';
import {
  Enterprise,
  Department,
  User,
  Role,
  Permission,
  UserRole
} from '../models';

/**
 * 初始化权限数据
 */
export const seedPermissions = async (): Promise<void> => {
  logger.info('开始初始化权限数据...');

  const permissions = [
    // 用户管理权限
    { name: '查看用户', code: 'user:view', module: 'user', description: '查看用户列表和详情' },
    { name: '创建用户', code: 'user:create', module: 'user', description: '创建新用户' },
    { name: '编辑用户', code: 'user:update', module: 'user', description: '编辑用户信息' },
    { name: '删除用户', code: 'user:delete', module: 'user', description: '删除用户' },
    { name: '管理用户状态', code: 'user:status', module: 'user', description: '启用/禁用用户' },

    // 企业管理权限
    { name: '查看企业', code: 'enterprise:view', module: 'enterprise', description: '查看企业信息' },
    { name: '创建企业', code: 'enterprise:create', module: 'enterprise', description: '创建新企业' },
    { name: '编辑企业', code: 'enterprise:update', module: 'enterprise', description: '编辑企业信息' },
    { name: '删除企业', code: 'enterprise:delete', module: 'enterprise', description: '删除企业' },

    // 部门管理权限
    { name: '查看部门', code: 'department:view', module: 'department', description: '查看部门结构' },
    { name: '创建部门', code: 'department:create', module: 'department', description: '创建新部门' },
    { name: '编辑部门', code: 'department:update', module: 'department', description: '编辑部门信息' },
    { name: '删除部门', code: 'department:delete', module: 'department', description: '删除部门' },

    // 角色管理权限
    { name: '查看角色', code: 'role:view', module: 'role', description: '查看角色列表' },
    { name: '创建角色', code: 'role:create', module: 'role', description: '创建新角色' },
    { name: '编辑角色', code: 'role:update', module: 'role', description: '编辑角色信息' },
    { name: '删除角色', code: 'role:delete', module: 'role', description: '删除角色' },
    { name: '分配权限', code: 'role:assign', module: 'role', description: '为角色分配权限' },

    // 权限管理权限
    { name: '查看权限', code: 'permission:view', module: 'permission', description: '查看权限列表' },

    // 订单管理权限
    { name: '查看订单', code: 'order:view', module: 'order', description: '查看订单信息' },
    { name: '创建订单', code: 'order:create', module: 'order', description: '创建新订单' },
    { name: '编辑订单', code: 'order:update', module: 'order', description: '编辑订单信息' },
    { name: '删除订单', code: 'order:delete', module: 'order', description: '删除订单' },

    // 生产计划权限
    { name: '查看生产计划', code: 'production:view', module: 'production', description: '查看生产计划' },
    { name: '创建生产计划', code: 'production:create', module: 'production', description: '创建生产计划' },
    { name: '编辑生产计划', code: 'production:update', module: 'production', description: '编辑生产计划' },

    // 库存管理权限
    { name: '查看库存', code: 'inventory:view', module: 'inventory', description: '查看库存信息' },
    { name: '管理库存', code: 'inventory:manage', module: 'inventory', description: '管理库存操作' },

    // 工资管理权限
    { name: '查看工资', code: 'salary:view', module: 'salary', description: '查看工资信息' },
    { name: '管理工资', code: 'salary:manage', module: 'salary', description: '管理工资操作' },

    // 数字空间权限
    { name: '查看数字空间', code: 'digital:view', module: 'digital', description: '查看数字空间' },
    { name: '管理数字空间', code: 'digital:manage', module: 'digital', description: '管理数字空间' },
  ];

  for (const permission of permissions) {
    await Permission.findOrCreate({
      where: { code: permission.code },
      defaults: permission,
    });
  }

  logger.info('权限数据初始化完成');
};

/**
 * 初始化系统角色
 */
export const seedSystemRoles = async (): Promise<void> => {
  logger.info('开始初始化系统角色...');

  // 创建超级管理员角色
  const [superAdminRole] = await Role.findOrCreate({
    where: { code: 'SUPER_ADMIN' },
    defaults: {
      name: '超级管理员',
      code: 'SUPER_ADMIN',
      description: '系统最高权限，可管理所有企业',
      isSystem: true,

    },
  });

  // 为超级管理员分配所有权限
  const allPermissions = await Permission.findAll();
  await superAdminRole.setPermissions(allPermissions);

  logger.info('系统角色初始化完成');
};

/**
 * 初始化默认企业和管理员
 */
export const seedDefaultData = async (): Promise<void> => {
  logger.info('开始初始化默认数据...');

  // 创建默认企业
  const [defaultEnterprise] = await Enterprise.findOrCreate({
    where: { code: 'DEFAULT' },
    defaults: {
      name: '默认企业',
      code: 'DEFAULT',
      description: '系统默认企业',
      status: 1,
    },
  });

  // 创建企业管理员角色
  const [enterpriseAdminRole] = await Role.findOrCreate({
    where: { 
      code: 'ENTERPRISE_ADMIN',
      enterpriseId: defaultEnterprise.id,
    },
    defaults: {
      name: '企业管理员',
      code: 'ENTERPRISE_ADMIN',
      description: '企业最高权限，可管理本企业所有资源',
      isSystem: false,
      enterpriseId: defaultEnterprise.id,
    },
  });

  // 为企业管理员分配权限（除了企业管理权限）
  const enterprisePermissions = await Permission.findAll({
    where: {
      module: ['user', 'department', 'role', 'permission', 'order', 'production', 'inventory', 'salary', 'digital'],
    },
  });
  await enterpriseAdminRole.setPermissions(enterprisePermissions);

  // 创建默认部门
  const [defaultDepartment] = await Department.findOrCreate({
    where: { 
      name: '总部',
      enterpriseId: defaultEnterprise.id,
    },
    defaults: {
      name: '总部',
      enterpriseId: defaultEnterprise.id,
      description: '企业总部',
      level: 1,
      path: '0',
      sortOrder: 0,
    },
  });

  // 创建默认管理员用户
  const [adminUser] = await User.findOrCreate({
    where: { username: 'admin' },
    defaults: {
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: 'admin123', // 将被自动加密
      realName: '系统管理员',
      enterpriseId: defaultEnterprise.id,
      departmentId: defaultDepartment.id,
      status: 1,
    },
  });

  // 为管理员分配企业管理员角色
  await UserRole.findOrCreate({
    where: {
      userId: adminUser.id,
      roleId: enterpriseAdminRole.id,
    },
    defaults: {
      userId: adminUser.id,
      roleId: enterpriseAdminRole.id,
    },
  });

  logger.info('默认数据初始化完成');
};

/**
 * 执行所有种子数据初始化
 */
export const runAllSeeders = async (): Promise<void> => {
  try {
    await seedPermissions();
    await seedSystemRoles();
    await seedDefaultData();
    logger.info('所有种子数据初始化完成');
  } catch (error) {
    logger.error('种子数据初始化失败', error);
    throw error;
  }
};
