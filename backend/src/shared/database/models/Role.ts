/**
 * 角色模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-002 数据库初始化, 创建角色模型; Principle_Applied: RBAC权限模型;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 角色属性接口
export interface RoleAttributes {
  id: number;
  enterpriseId?: number; // null表示系统级角色
  name: string;
  code: string;
  description?: string;
  isSystem: boolean; // 是否为系统预定义角色
  createdAt: Date;
  updatedAt: Date;
}

// 创建角色时的可选属性
export interface RoleCreationAttributes 
  extends Optional<RoleAttributes, 'id' | 'enterpriseId' | 'description' | 'isSystem' | 'createdAt' | 'updatedAt'> {}

// 角色模型类
export class Role extends Model<RoleAttributes, RoleCreationAttributes>
  implements RoleAttributes {
  public id!: number;
  public enterpriseId?: number;
  public name!: string;
  public code!: string;
  public description?: string;
  public isSystem!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联方法声明
  public getEnterprise!: () => Promise<any>;
  public getUsers!: () => Promise<any[]>;
  public getPermissions!: () => Promise<any[]>;
  public addPermissions!: (permissions: any[]) => Promise<void>;
  public removePermissions!: (permissions: any[]) => Promise<void>;
  public setPermissions!: (permissions: any[]) => Promise<void>;
}

// 定义角色模型
Role.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '角色ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '所属企业ID，null表示系统级角色',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '角色名称',
      validate: {
        notEmpty: {
          msg: '角色名称不能为空',
        },
        len: {
          args: [2, 50],
          msg: '角色名称长度必须在2-50个字符之间',
        },
      },
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '角色编码',
      validate: {
        notEmpty: {
          msg: '角色编码不能为空',
        },
        is: {
          args: /^[A-Z_][A-Z0-9_]*$/,
          msg: '角色编码只能包含大写字母、数字和下划线，且必须以字母或下划线开头',
        },
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '角色描述',
    },
    isSystem: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否为系统预定义角色',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间',
    },
  },
  {
    sequelize,
    modelName: 'Role',
    tableName: 'em_roles',
    timestamps: true,
    underscored: true,
    comment: '角色表',
    indexes: [
      {
        fields: ['enterprise_id'],
        name: 'roles_enterprise_id_idx',
      },
      {
        fields: ['code'],
        name: 'roles_code_idx',
      },
      {
        fields: ['is_system'],
        name: 'roles_is_system_idx',
      },
      {
        fields: ['enterprise_id', 'code'],
        unique: true,
        name: 'roles_enterprise_code_unique_idx',
      },
    ],
    validate: {
      // 系统角色不能属于特定企业
      systemRoleValidation() {
        if (this['isSystem'] && this['enterpriseId'] !== null) {
          throw new Error('系统角色不能属于特定企业');
        }
      },
      // 非系统角色必须属于某个企业
      enterpriseRoleValidation() {
        if (!this['isSystem'] && this['enterpriseId'] === null) {
          throw new Error('企业角色必须属于某个企业');
        }
      },
    },
  }
);
