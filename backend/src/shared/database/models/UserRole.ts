/**
 * 用户角色关联模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-002 数据库初始化, 创建用户角色关联模型; Principle_Applied: 多对多关联表设计;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 用户角色关联属性接口
export interface UserRoleAttributes {
  id: number;
  userId: number;
  roleId: number;
  createdAt: Date;
  updatedAt: Date;
}

// 创建用户角色关联时的可选属性
export interface UserRoleCreationAttributes 
  extends Optional<UserRoleAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// 用户角色关联模型类
export class UserRole extends Model<UserRoleAttributes, UserRoleCreationAttributes>
  implements UserRoleAttributes {
  public id!: number;
  public userId!: number;
  public roleId!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联方法声明
  public getUser!: () => Promise<any>;
  public getRole!: () => Promise<any>;
}

// 定义用户角色关联模型
UserRole.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '关联ID',
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '用户ID',
      references: {
        model: 'em_users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    roleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '角色ID',
      references: {
        model: 'em_roles',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间',
    },
  },
  {
    sequelize,
    modelName: 'UserRole',
    tableName: 'em_user_roles',
    timestamps: true,
    underscored: true,
    comment: '用户角色关联表',
    indexes: [
      {
        fields: ['user_id'],
        name: 'user_roles_user_id_idx',
      },
      {
        fields: ['role_id'],
        name: 'user_roles_role_id_idx',
      },
      {
        unique: true,
        fields: ['user_id', 'role_id'],
        name: 'user_roles_user_role_unique_idx',
      },
    ],
  }
);
