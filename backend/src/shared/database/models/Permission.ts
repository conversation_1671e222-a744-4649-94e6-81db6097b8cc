/**
 * 权限模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-002 数据库初始化, 创建权限模型; Principle_Applied: RBAC权限模型;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 权限属性接口
export interface PermissionAttributes {
  id: number;
  name: string;
  code: string;
  module: string; // 所属模块
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 创建权限时的可选属性
export interface PermissionCreationAttributes 
  extends Optional<PermissionAttributes, 'id' | 'description' | 'createdAt' | 'updatedAt'> {}

// 权限模型类
export class Permission extends Model<PermissionAttributes, PermissionCreationAttributes>
  implements PermissionAttributes {
  public id!: number;
  public name!: string;
  public code!: string;
  public module!: string;
  public description?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联方法声明
  public getRoles!: () => Promise<any[]>;
}

// 定义权限模型
Permission.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '权限ID',
    },
    name: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '权限名称',
      validate: {
        notEmpty: {
          msg: '权限名称不能为空',
        },
        len: {
          args: [2, 50],
          msg: '权限名称长度必须在2-50个字符之间',
        },
      },
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: {
        name: 'permissions_code_unique',
        msg: '权限编码已存在',
      },
      comment: '权限编码',
      validate: {
        notEmpty: {
          msg: '权限编码不能为空',
        },
        is: {
          args: /^[a-z][a-z0-9_]*:[a-z][a-z0-9_]*$/,
          msg: '权限编码格式应为：模块:操作，如：user:create',
        },
      },
    },
    module: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '所属模块',
      validate: {
        notEmpty: {
          msg: '所属模块不能为空',
        },
        isIn: {
          args: [['user', 'enterprise', 'department', 'role', 'permission', 'order', 'production', 'inventory', 'salary', 'digital']],
          msg: '模块名称不在允许的范围内',
        },
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '权限描述',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间',
    },
  },
  {
    sequelize,
    modelName: 'Permission',
    tableName: 'em_permissions',
    timestamps: true,
    underscored: true,
    comment: '权限表',
    indexes: [
      {
        unique: true,
        fields: ['code'],
        name: 'permissions_code_unique_idx',
      },
      {
        fields: ['module'],
        name: 'permissions_module_idx',
      },
    ],
  }
);
