/**
 * 企业模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-002 数据库初始化, 创建企业模型; Principle_Applied: 数据模型设计;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 企业属性接口
export interface EnterpriseAttributes {
  id: number;
  name: string;
  code: string;
  logoUrl?: string;
  description?: string;
  status: number; // 1:正常 0:禁用
  createdAt: Date;
  updatedAt: Date;
}

// 创建企业时的可选属性
export interface EnterpriseCreationAttributes 
  extends Optional<EnterpriseAttributes, 'id' | 'logoUrl' | 'description' | 'status' | 'createdAt' | 'updatedAt'> {}

// 企业模型类
export class Enterprise extends Model<EnterpriseAttributes, EnterpriseCreationAttributes>
  implements EnterpriseAttributes {
  public id!: number;
  public name!: string;
  public code!: string;
  public logoUrl?: string;
  public description?: string;
  public status!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联方法声明
  public getDepartments!: () => Promise<any[]>;
  public getUsers!: () => Promise<any[]>;
  public getRoles!: () => Promise<any[]>;
}

// 定义企业模型
Enterprise.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '企业ID',
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '企业名称',
      validate: {
        notEmpty: {
          msg: '企业名称不能为空',
        },
        len: {
          args: [2, 100],
          msg: '企业名称长度必须在2-100个字符之间',
        },
      },
    },
    code: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: {
        name: 'enterprises_code_unique',
        msg: '企业编码已存在',
      },
      comment: '企业编码',
      validate: {
        notEmpty: {
          msg: '企业编码不能为空',
        },
        is: {
          args: /^[A-Z0-9_]+$/,
          msg: '企业编码只能包含大写字母、数字和下划线',
        },
      },
    },
    logoUrl: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '企业Logo URL',
      validate: {
        isUrl: {
          msg: 'Logo URL格式不正确',
        },
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '企业描述',
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '状态：1-正常，0-禁用',
      validate: {
        isIn: {
          args: [[0, 1]],
          msg: '状态值必须为0或1',
        },
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间',
    },
  },
  {
    sequelize,
    modelName: 'Enterprise',
    tableName: 'em_enterprises',
    timestamps: true,
    underscored: true,
    comment: '企业表',
    indexes: [
      {
        unique: true,
        fields: ['code'],
        name: 'enterprises_code_unique_idx',
      },
      {
        fields: ['status'],
        name: 'enterprises_status_idx',
      },
      {
        fields: ['created_at'],
        name: 'enterprises_created_at_idx',
      },
    ],
  }
);
