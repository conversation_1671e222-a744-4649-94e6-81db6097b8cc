/**
 * 用户模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-002 数据库初始化, 创建用户模型; Principle_Applied: 用户数据模型设计;}}
 */

import { DataTypes, Model, Optional, Op } from 'sequelize';
import { sequelize } from '../index';
import bcrypt from 'bcryptjs';
import { config } from '@/config/app.config';

// 用户属性接口
export interface UserAttributes {
  id: number;
  enterpriseId: number;
  departmentId?: number;
  username: string;
  email?: string;
  phone?: string;
  passwordHash: string;
  realName: string;
  avatarUrl?: string;
  status: number; // 1:正常 0:禁用
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 创建用户时的可选属性
export interface UserCreationAttributes 
  extends Optional<UserAttributes, 'id' | 'departmentId' | 'email' | 'phone' | 'avatarUrl' | 'status' | 'lastLoginAt' | 'createdAt' | 'updatedAt'> {}

// 用户模型类
export class User extends Model<UserAttributes, UserCreationAttributes>
  implements UserAttributes {
  public id!: number;
  public enterpriseId!: number;
  public departmentId?: number;
  public username!: string;
  public email?: string;
  public phone?: string;
  public passwordHash!: string;
  public realName!: string;
  public avatarUrl?: string;
  public status!: number;
  public lastLoginAt?: Date;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联方法声明
  public getEnterprise!: () => Promise<any>;
  public getDepartment!: () => Promise<any>;
  public getRoles!: () => Promise<any[]>;
  public addRoles!: (roles: any[]) => Promise<void>;
  public removeRoles!: (roles: any[]) => Promise<void>;
  public setRoles!: (roles: any[]) => Promise<void>;

  // 实例方法
  public async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.passwordHash);
  }

  public async updateLastLogin(): Promise<void> {
    this.lastLoginAt = new Date();
    await this.save();
  }

  // 静态方法
  public static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, config.bcrypt.rounds);
  }
}

// 定义用户模型
User.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '用户ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '所属企业ID',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    departmentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '所属部门ID',
      references: {
        model: 'em_departments',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: {
        name: 'users_username_unique',
        msg: '用户名已存在',
      },
      comment: '用户名',
      validate: {
        notEmpty: {
          msg: '用户名不能为空',
        },
        len: {
          args: [3, 50],
          msg: '用户名长度必须在3-50个字符之间',
        },
        is: {
          args: /^[a-zA-Z0-9_]+$/,
          msg: '用户名只能包含字母、数字和下划线',
        },
      },
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: true,
      unique: {
        name: 'users_email_unique',
        msg: '邮箱已存在',
      },
      comment: '邮箱',
      validate: {
        isEmail: {
          msg: '邮箱格式不正确',
        },
      },
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      unique: {
        name: 'users_phone_unique',
        msg: '手机号已存在',
      },
      comment: '手机号',
      validate: {
        is: {
          args: /^1[3-9]\d{9}$/,
          msg: '手机号格式不正确',
        },
      },
    },
    passwordHash: {
      type: DataTypes.STRING(255),
      allowNull: false,
      comment: '密码哈希',
    },
    realName: {
      type: DataTypes.STRING(50),
      allowNull: false,
      comment: '真实姓名',
      validate: {
        notEmpty: {
          msg: '真实姓名不能为空',
        },
        len: {
          args: [2, 50],
          msg: '真实姓名长度必须在2-50个字符之间',
        },
      },
    },
    avatarUrl: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: '头像URL',
      validate: {
        isUrl: {
          msg: '头像URL格式不正确',
        },
      },
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '状态：1-正常，0-禁用',
      validate: {
        isIn: {
          args: [[0, 1]],
          msg: '状态值必须为0或1',
        },
      },
    },
    lastLoginAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后登录时间',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间',
    },
  },
  {
    sequelize,
    modelName: 'User',
    tableName: 'em_users',
    timestamps: true,
    underscored: true,
    comment: '用户表',
    indexes: [
      {
        unique: true,
        fields: ['username'],
        name: 'users_username_unique_idx',
      },
      {
        unique: true,
        fields: ['email'],
        name: 'users_email_unique_idx',
        where: {
          email: {
            [Op.ne]: null,
          },
        },
      },
      {
        unique: true,
        fields: ['phone'],
        name: 'users_phone_unique_idx',
        where: {
          phone: {
            [Op.ne]: null,
          },
        },
      },
      {
        fields: ['enterprise_id'],
        name: 'users_enterprise_id_idx',
      },
      {
        fields: ['department_id'],
        name: 'users_department_id_idx',
      },
      {
        fields: ['status'],
        name: 'users_status_idx',
      },
    ],
    hooks: {
      beforeCreate: async (user: User) => {
        // 自动加密密码
        if (user.passwordHash && !user.passwordHash.startsWith('$2')) {
          user.passwordHash = await User.hashPassword(user.passwordHash);
        }
      },
      beforeUpdate: async (user: User) => {
        // 如果密码发生变化，重新加密
        if (user.changed('passwordHash') && user.passwordHash && !user.passwordHash.startsWith('$2')) {
          user.passwordHash = await User.hashPassword(user.passwordHash);
        }
      },
    },
    defaultScope: {
      attributes: {
        exclude: ['passwordHash'],
      },
    },
    scopes: {
      withPassword: {
        attributes: {
          include: ['passwordHash'],
        },
      },
    },
  }
);
