/**
 * 部门模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-002 数据库初始化, 创建部门模型; Principle_Applied: 树形结构数据模型;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 部门属性接口
export interface DepartmentAttributes {
  id: number;
  enterpriseId: number;
  name: string;
  parentId?: number;
  description?: string;
  level: number;
  path: string; // 层级路径，如：1/2/3
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

// 创建部门时的可选属性
export interface DepartmentCreationAttributes 
  extends Optional<DepartmentAttributes, 'id' | 'parentId' | 'description' | 'level' | 'path' | 'sortOrder' | 'createdAt' | 'updatedAt'> {}

// 部门模型类
export class Department extends Model<DepartmentAttributes, DepartmentCreationAttributes>
  implements DepartmentAttributes {
  public id!: number;
  public enterpriseId!: number;
  public name!: string;
  public parentId?: number;
  public description?: string;
  public level!: number;
  public path!: string;
  public sortOrder!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联方法声明
  public getEnterprise!: () => Promise<any>;
  public getParent!: () => Promise<Department | null>;
  public getChildren!: () => Promise<Department[]>;
  public getUsers!: () => Promise<any[]>;
}

// 定义部门模型
Department.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '部门ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '所属企业ID',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      comment: '部门名称',
      validate: {
        notEmpty: {
          msg: '部门名称不能为空',
        },
        len: {
          args: [2, 100],
          msg: '部门名称长度必须在2-100个字符之间',
        },
      },
    },
    parentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '上级部门ID',
      references: {
        model: 'em_departments',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '部门描述',
    },
    level: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '部门层级',
      validate: {
        min: {
          args: [1],
          msg: '部门层级不能小于1',
        },
        max: {
          args: [10],
          msg: '部门层级不能超过10级',
        },
      },
    },
    path: {
      type: DataTypes.STRING(500),
      allowNull: false,
      comment: '层级路径，如：1/2/3',
      validate: {
        notEmpty: {
          msg: '层级路径不能为空',
        },
      },
    },
    sortOrder: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '排序顺序',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间',
    },
  },
  {
    sequelize,
    modelName: 'Department',
    tableName: 'em_departments',
    timestamps: true,
    underscored: true,
    comment: '部门表',
    indexes: [
      {
        fields: ['enterprise_id'],
        name: 'departments_enterprise_id_idx',
      },
      {
        fields: ['parent_id'],
        name: 'departments_parent_id_idx',
      },
      {
        fields: ['level'],
        name: 'departments_level_idx',
      },
      {
        fields: ['path'],
        name: 'departments_path_idx',
      },
      {
        fields: ['enterprise_id', 'name'],
        unique: true,
        name: 'departments_enterprise_name_unique_idx',
      },
    ],
    hooks: {
      beforeCreate: async (department: Department) => {
        // 自动计算层级和路径
        if (department.parentId) {
          const parent = await Department.findByPk(department.parentId);
          if (parent) {
            department.level = parent.level + 1;
            department.path = `${parent.path}/${department.parentId}`;
          }
        } else {
          department.level = 1;
          department.path = '0'; // 根部门路径为0
        }
      },
      beforeUpdate: async (department: Department) => {
        // 如果父部门发生变化，重新计算层级和路径
        if (department.changed('parentId')) {
          if (department.parentId) {
            const parent = await Department.findByPk(department.parentId);
            if (parent) {
              department.level = parent.level + 1;
              department.path = `${parent.path}/${department.parentId}`;
            }
          } else {
            department.level = 1;
            department.path = '0';
          }
        }
      },
    },
  }
);
