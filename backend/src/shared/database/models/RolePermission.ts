/**
 * 角色权限关联模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-002 数据库初始化, 创建角色权限关联模型; Principle_Applied: 多对多关联表设计;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 角色权限关联属性接口
export interface RolePermissionAttributes {
  id: number;
  roleId: number;
  permissionId: number;
  createdAt: Date;
  updatedAt: Date;
}

// 创建角色权限关联时的可选属性
export interface RolePermissionCreationAttributes 
  extends Optional<RolePermissionAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// 角色权限关联模型类
export class RolePermission extends Model<RolePermissionAttributes, RolePermissionCreationAttributes>
  implements RolePermissionAttributes {
  public id!: number;
  public roleId!: number;
  public permissionId!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联方法声明
  public getRole!: () => Promise<any>;
  public getPermission!: () => Promise<any>;
}

// 定义角色权限关联模型
RolePermission.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '关联ID',
    },
    roleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '角色ID',
      references: {
        model: 'em_roles',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    permissionId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '权限ID',
      references: {
        model: 'em_permissions',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: '更新时间',
    },
  },
  {
    sequelize,
    modelName: 'RolePermission',
    tableName: 'em_role_permissions',
    timestamps: true,
    underscored: true,
    comment: '角色权限关联表',
    indexes: [
      {
        fields: ['role_id'],
        name: 'role_permissions_role_id_idx',
      },
      {
        fields: ['permission_id'],
        name: 'role_permissions_permission_id_idx',
      },
      {
        unique: true,
        fields: ['role_id', 'permission_id'],
        name: 'role_permissions_role_permission_unique_idx',
      },
    ],
  }
);
