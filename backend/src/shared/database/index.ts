/**
 * 数据库连接配置
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-002 数据库初始化, 创建数据库连接; Principle_Applied: 数据库连接池管理;}}
 */

import { Sequelize } from 'sequelize';
import { config } from '@/config/app.config';
import { logger } from '@/shared/utils/logger';

// 创建Sequelize实例
export const sequelize = new Sequelize({
  host: config.database.host,
  port: config.database.port,
  database: config.database.name,
  username: config.database.username,
  password: config.database.password,
  dialect: 'postgres',
  
  // 连接池配置
  pool: {
    max: config.database.pool.max,
    min: config.database.pool.min,
    acquire: config.database.pool.acquire,
    idle: config.database.pool.idle,
  },
  
  // 日志配置
  logging: (sql: string) => {
    if (config.app.nodeEnv === 'development') {
      logger.debug('SQL执行', { sql });
    }
  },
  
  // 其他配置
  define: {
    // 自动添加时间戳字段
    timestamps: true,
    // 使用下划线命名
    underscored: true,
    // 禁用复数表名
    freezeTableName: true,
    // 软删除
    paranoid: false,
  },
  
  // 时区配置
  timezone: '+08:00',
});

/**
 * 测试数据库连接
 */
export const testConnection = async (): Promise<void> => {
  try {
    await sequelize.authenticate();
    logger.info('数据库连接成功');
  } catch (error) {
    logger.error('数据库连接失败', error);
    throw error;
  }
};

/**
 * 同步数据库模型
 */
export const syncDatabase = async (force: boolean = false): Promise<void> => {
  try {
    await sequelize.sync({ force });
    logger.info(`数据库同步完成 ${force ? '(强制重建)' : ''}`);
  } catch (error) {
    logger.error('数据库同步失败', error);
    throw error;
  }
};

/**
 * 关闭数据库连接
 */
export const closeConnection = async (): Promise<void> => {
  try {
    await sequelize.close();
    logger.info('数据库连接已关闭');
  } catch (error) {
    logger.error('关闭数据库连接失败', error);
    throw error;
  }
};
