/**
 * 请求日志中间件
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-001 项目初始化, 创建请求日志中间件; Principle_Applied: 请求追踪;}}
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/shared/utils/logger';
import { randomUUID } from 'crypto';

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      startTime?: number;
    }
  }
}

/**
 * 请求日志中间件
 */
export const requestLoggerMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // 生成请求ID
  req.requestId = randomUUID();
  req.startTime = Date.now();

  // 记录请求开始
  logger.info('请求开始', {
    requestId: req.requestId,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length'),
  });

  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - (req.startTime || 0);
    
    logger.info('请求结束', {
      requestId: req.requestId,
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length'),
    });
  });

  next();
};
