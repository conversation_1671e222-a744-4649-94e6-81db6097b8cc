/**
 * 限流中间件
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-001 项目初始化, 创建限流中间件; Principle_Applied: 安全防护;}}
 */

import rateLimit from 'express-rate-limit';
import { config } from '@/config/app.config';

/**
 * 通用限流中间件
 */
export const rateLimitMiddleware = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests,
  message: {
    code: 429,
    message: '请求过于频繁，请稍后再试',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // 跳过健康检查接口
    return req.path === '/health';
  },
});

/**
 * 登录接口限流中间件
 */
export const loginRateLimitMiddleware = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次尝试
  message: {
    code: 429,
    message: '登录尝试过于频繁，请15分钟后再试',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // 成功的请求不计入限制
});

/**
 * 注册接口限流中间件
 */
export const registerRateLimitMiddleware = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 最多3次注册
  message: {
    code: 429,
    message: '注册过于频繁，请1小时后再试',
    timestamp: new Date().toISOString(),
  },
  standardHeaders: true,
  legacyHeaders: false,
});
