/**
 * 认证中间件
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-003 认证系统, 创建认证中间件; Principle_Applied: JWT认证验证;}}
 */

import { Request, Response, NextFunction } from 'express';
import { verifyAccessToken, extractTokenFromHeader, JwtPayload } from '@/shared/utils/jwt';
import { createApiError } from './error.middleware';
import { logger } from '@/shared/utils/logger';

// 扩展Request接口，添加用户信息
declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload;
    }
  }
}

/**
 * JWT认证中间件
 */
export const authMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    // 从请求头中提取令牌
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      throw createApiError('未提供认证令牌', 401, 'MISSING_TOKEN');
    }

    // 验证令牌
    const payload = verifyAccessToken(token);
    
    // 将用户信息添加到请求对象
    req.user = payload;
    
    // 记录认证成功日志
    logger.debug('用户认证成功', {
      userId: payload.userId,
      username: payload.username,
      enterpriseId: payload.enterpriseId,
      requestId: req.requestId,
    });
    
    next();
  } catch (error) {
    logger.warn('用户认证失败', {
      error: error instanceof Error ? error.message : '未知错误',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: req.requestId,
    });
    
    if (error instanceof Error) {
      next(createApiError(error.message, 401, 'AUTH_FAILED'));
    } else {
      next(createApiError('认证失败', 401, 'AUTH_FAILED'));
    }
  }
};

/**
 * 可选认证中间件（令牌存在时验证，不存在时跳过）
 */
export const optionalAuthMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (token) {
      const payload = verifyAccessToken(token);
      req.user = payload;
    }
    
    next();
  } catch (error) {
    // 可选认证失败时不阻止请求，但记录日志
    logger.warn('可选认证失败', {
      error: error instanceof Error ? error.message : '未知错误',
      ip: req.ip,
      requestId: req.requestId,
    });
    
    next();
  }
};

/**
 * 企业权限验证中间件
 * 验证用户是否属于指定企业或具有跨企业权限
 */
export const enterpriseMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  try {
    if (!req.user) {
      throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
    }

    const { enterpriseId } = req.params;
    const userEnterpriseId = req.user.enterpriseId;

    // 如果请求中包含企业ID参数，验证用户是否属于该企业
    if (enterpriseId) {
      const requestedEnterpriseId = parseInt(enterpriseId, 10);
      
      // 检查用户是否属于请求的企业，或者是否具有超级管理员权限
      const isSuperAdmin = req.user.roles.includes('SUPER_ADMIN');
      const belongsToEnterprise = userEnterpriseId === requestedEnterpriseId;
      
      if (!isSuperAdmin && !belongsToEnterprise) {
        throw createApiError('无权访问该企业资源', 403, 'ENTERPRISE_ACCESS_DENIED');
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * 权限验证中间件工厂
 * 创建验证特定权限的中间件
 */
export const permissionMiddleware = (requiredPermissions: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
      }

      const userPermissions = req.user.permissions || [];
      const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];

      // 检查用户是否具有所需权限
      const hasAllPermissions = permissions.every(permission => 
        userPermissions.includes(permission)
      );

      // 超级管理员拥有所有权限
      const isSuperAdmin = req.user.roles.includes('SUPER_ADMIN');

      if (!isSuperAdmin && !hasAllPermissions) {
        const missingPermissions = permissions.filter(permission => 
          !userPermissions.includes(permission)
        );
        
        logger.warn('权限验证失败', {
          userId: req.user.userId,
          username: req.user.username,
          requiredPermissions: permissions,
          userPermissions,
          missingPermissions,
          requestId: req.requestId,
        });

        throw createApiError(
          `权限不足，缺少权限: ${missingPermissions.join(', ')}`, 
          403, 
          'INSUFFICIENT_PERMISSIONS'
        );
      }

      logger.debug('权限验证成功', {
        userId: req.user.userId,
        username: req.user.username,
        requiredPermissions: permissions,
        requestId: req.requestId,
      });

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * 角色验证中间件工厂
 * 创建验证特定角色的中间件
 */
export const roleMiddleware = (requiredRoles: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
      }

      const userRoles = req.user.roles || [];
      const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];

      // 检查用户是否具有所需角色
      const hasAnyRole = roles.some(role => userRoles.includes(role));

      if (!hasAnyRole) {
        logger.warn('角色验证失败', {
          userId: req.user.userId,
          username: req.user.username,
          requiredRoles: roles,
          userRoles,
          requestId: req.requestId,
        });

        throw createApiError(
          `角色权限不足，需要角色: ${roles.join(' 或 ')}`, 
          403, 
          'INSUFFICIENT_ROLE'
        );
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};
