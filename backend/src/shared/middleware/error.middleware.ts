/**
 * 全局错误处理中间件
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-001 项目初始化, 创建错误处理中间件; Principle_Applied: 统一错误处理;}}
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/shared/utils/logger';
import { config } from '@/config/app.config';

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

/**
 * 创建API错误
 */
export const createApiError = (
  message: string,
  statusCode: number = 500,
  code?: string,
  details?: any
): ApiError => {
  const error = new Error(message) as ApiError;
  error.statusCode = statusCode;
  error.code = code;
  error.details = details;
  return error;
};

/**
 * 全局错误处理中间件
 */
export const errorHandler = (
  error: ApiError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // 记录错误日志
  logger.error('API错误', {
    message: error.message,
    stack: error.stack,
    statusCode: error.statusCode,
    code: error.code,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body,
    query: req.query,
    params: req.params,
  });

  // 设置默认错误状态码
  const statusCode = error.statusCode || 500;
  const code = error.code || 'INTERNAL_SERVER_ERROR';

  // 构建错误响应
  const errorResponse: any = {
    code: statusCode,
    message: error.message || '服务器内部错误',
    timestamp: new Date().toISOString(),
  };

  // 开发环境下返回详细错误信息
  if (config.app.nodeEnv === 'development') {
    errorResponse.stack = error.stack;
    errorResponse.details = error.details;
  }

  // 特定错误类型处理
  switch (error.name) {
    case 'ValidationError':
      errorResponse.code = 400;
      errorResponse.message = '数据验证失败';
      errorResponse.details = error.details;
      break;

    case 'UnauthorizedError':
    case 'JsonWebTokenError':
    case 'TokenExpiredError':
      errorResponse.code = 401;
      errorResponse.message = '认证失败';
      break;

    case 'ForbiddenError':
      errorResponse.code = 403;
      errorResponse.message = '权限不足';
      break;

    case 'NotFoundError':
      errorResponse.code = 404;
      errorResponse.message = '资源不存在';
      break;

    case 'ConflictError':
      errorResponse.code = 409;
      errorResponse.message = '资源冲突';
      break;

    case 'SequelizeValidationError':
      errorResponse.code = 400;
      errorResponse.message = '数据验证失败';
      errorResponse.details = error.details;
      break;

    case 'SequelizeUniqueConstraintError':
      errorResponse.code = 409;
      errorResponse.message = '数据已存在';
      break;

    case 'SequelizeForeignKeyConstraintError':
      errorResponse.code = 400;
      errorResponse.message = '关联数据不存在';
      break;

    case 'MulterError':
      errorResponse.code = 400;
      if (error.code === 'LIMIT_FILE_SIZE') {
        errorResponse.message = '文件大小超出限制';
      } else if (error.code === 'LIMIT_FILE_COUNT') {
        errorResponse.message = '文件数量超出限制';
      } else {
        errorResponse.message = '文件上传失败';
      }
      break;

    default:
      // 生产环境下隐藏内部错误详情
      if (config.app.nodeEnv === 'production' && statusCode === 500) {
        errorResponse.message = '服务器内部错误';
      }
  }

  res.status(statusCode).json(errorResponse);
};

/**
 * 异步错误处理包装器
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 404错误处理
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  res.status(404).json({
    code: 404,
    message: `路由 ${req.method} ${req.url} 不存在`,
    timestamp: new Date().toISOString(),
  });
};
