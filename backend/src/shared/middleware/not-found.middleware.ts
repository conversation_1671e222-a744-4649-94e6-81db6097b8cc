/**
 * 404错误处理中间件
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-001 项目初始化, 创建404处理中间件; Principle_Applied: 统一错误处理;}}
 */

import { Request, Response } from 'express';

/**
 * 404错误处理中间件
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  res.status(404).json({
    code: 404,
    message: `路由 ${req.method} ${req.url} 不存在`,
    timestamp: new Date().toISOString(),
    path: req.url,
    method: req.method,
  });
};
