/**
 * API路由入口
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-001 项目初始化, 创建路由入口; Principle_Applied: 模块化路由管理;}}
 */

import { Router } from 'express';
import { authRoutes } from '@/modules/auth/auth.routes';

const router = Router();

// 基础路由
router.get('/', (_req, res) => {
  res.json({
    code: 200,
    message: '刺绣管理系统API服务',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    docs: '/api/v1/docs',
    endpoints: {
      auth: '/api/v1/auth',
      health: '/health',
    },
  });
});

// 认证路由
router.use('/auth', authRoutes);

// TODO: 在后续任务中添加具体的业务路由
// router.use('/users', userRoutes);
// router.use('/enterprises', enterpriseRoutes);
// router.use('/departments', departmentRoutes);
// router.use('/roles', roleRoutes);
// router.use('/permissions', permissionRoutes);

export { router as apiRoutes };
