/**
 * JWT工具类
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-003 认证系统, 创建JWT工具; Principle_Applied: 无状态认证;}}
 */

import jwt from 'jsonwebtoken';
import { config } from '@/config/app.config';
import { logger } from './logger';

// JWT载荷接口
export interface JwtPayload {
  userId: number;
  username: string;
  enterpriseId: number;
  roles: string[];
  permissions: string[];
  iat?: number;
  exp?: number;
}

// 刷新令牌载荷接口
export interface RefreshTokenPayload {
  userId: number;
  username: string;
  tokenVersion: number;
  iat?: number;
  exp?: number;
}

/**
 * 生成访问令牌
 */
export const generateAccessToken = (payload: Omit<JwtPayload, 'iat' | 'exp'>): string => {
  try {
    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
      issuer: 'embroidery-management',
      audience: 'embroidery-management-client',
    });
  } catch (error) {
    logger.error('生成访问令牌失败', error);
    throw new Error('生成访问令牌失败');
  }
};

/**
 * 生成刷新令牌
 */
export const generateRefreshToken = (payload: Omit<RefreshTokenPayload, 'iat' | 'exp'>): string => {
  try {
    return jwt.sign(payload, config.jwt.refreshSecret, {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: 'embroidery-management',
      audience: 'embroidery-management-client',
    });
  } catch (error) {
    logger.error('生成刷新令牌失败', error);
    throw new Error('生成刷新令牌失败');
  }
};

/**
 * 验证访问令牌
 */
export const verifyAccessToken = (token: string): JwtPayload => {
  try {
    const decoded = jwt.verify(token, config.jwt.secret, {
      issuer: 'embroidery-management',
      audience: 'embroidery-management-client',
    }) as JwtPayload;
    
    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('访问令牌已过期');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('无效的访问令牌');
    } else {
      logger.error('验证访问令牌失败', error);
      throw new Error('验证访问令牌失败');
    }
  }
};

/**
 * 验证刷新令牌
 */
export const verifyRefreshToken = (token: string): RefreshTokenPayload => {
  try {
    const decoded = jwt.verify(token, config.jwt.refreshSecret, {
      issuer: 'embroidery-management',
      audience: 'embroidery-management-client',
    }) as RefreshTokenPayload;
    
    return decoded;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new Error('刷新令牌已过期');
    } else if (error instanceof jwt.JsonWebTokenError) {
      throw new Error('无效的刷新令牌');
    } else {
      logger.error('验证刷新令牌失败', error);
      throw new Error('验证刷新令牌失败');
    }
  }
};

/**
 * 解码令牌（不验证签名）
 */
export const decodeToken = (token: string): any => {
  try {
    return jwt.decode(token);
  } catch (error) {
    logger.error('解码令牌失败', error);
    throw new Error('解码令牌失败');
  }
};

/**
 * 检查令牌是否即将过期（剩余时间少于5分钟）
 */
export const isTokenExpiringSoon = (token: string): boolean => {
  try {
    const decoded = decodeToken(token);
    if (!decoded || !decoded.exp) {
      return true;
    }
    
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = decoded.exp - currentTime;
    
    // 如果剩余时间少于5分钟（300秒），认为即将过期
    return timeUntilExpiry < 300;
  } catch (error) {
    return true;
  }
};

/**
 * 从请求头中提取令牌
 */
export const extractTokenFromHeader = (authHeader?: string): string | null => {
  if (!authHeader) {
    return null;
  }
  
  const parts = authHeader.split(' ');
  if (parts.length !== 2 || parts[0] !== 'Bearer') {
    return null;
  }
  
  return parts[1];
};
