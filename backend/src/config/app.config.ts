/**
 * 应用配置文件
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-001 项目初始化, 创建应用配置; Principle_Applied: 配置集中管理;}}
 */

import dotenv from 'dotenv';

dotenv.config();

interface AppConfig {
  app: {
    nodeEnv: string;
    port: number;
    apiPrefix: string;
  };
  database: {
    host: string;
    port: number;
    name: string;
    username: string;
    password: string;
    dialect: string;
    tablePrefix: string;
    pool: {
      max: number;
      min: number;
      acquire: number;
      idle: number;
    };
  };
  jwt: {
    secret: string;
    expiresIn: string;
    refreshSecret: string;
    refreshExpiresIn: string;
  };
  bcrypt: {
    rounds: number;
  };
  upload: {
    path: string;
    maxFileSize: number;
    allowedTypes: string[];
  };
  logging: {
    level: string;
    filePath: string;
  };
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };
  cors: {
    origin: string;
    credentials: boolean;
  };
  security: {
    helmetEnabled: boolean;
    compressionEnabled: boolean;
  };
}

export const config: AppConfig = {
  app: {
    nodeEnv: process.env['NODE_ENV'] || 'development',
    port: parseInt(process.env['PORT'] || '3002', 10),
    apiPrefix: process.env['API_PREFIX'] || '/api/v1',
  },
  database: {
    host: process.env['DB_HOST'] || 'localhost',
    port: parseInt(process.env['DB_PORT'] || '5432', 10),
    name: process.env['DB_NAME'] || 'embroidery_management',
    username: process.env['DB_USER'] || process.env['DB_USERNAME'] || 'postgres',
    password: process.env['DB_PASSWORD'] || '',
    dialect: process.env['DB_DIALECT'] || 'postgres',
    tablePrefix: process.env['DB_TABLE_PREFIX'] || 'em_',
    pool: {
      max: parseInt(process.env['DB_POOL_MAX'] || '20', 10),
      min: parseInt(process.env['DB_POOL_MIN'] || '5', 10),
      acquire: parseInt(process.env['DB_POOL_ACQUIRE'] || '30000', 10),
      idle: parseInt(process.env['DB_POOL_IDLE'] || '10000', 10),
    },
  },
  jwt: {
    secret: process.env['JWT_SECRET'] || 'your_super_secret_jwt_key_here',
    expiresIn: process.env['JWT_EXPIRES_IN'] || '24h',
    refreshSecret: process.env['JWT_REFRESH_SECRET'] || 'your_refresh_token_secret_here',
    refreshExpiresIn: process.env['JWT_REFRESH_EXPIRES_IN'] || '7d',
  },
  bcrypt: {
    rounds: parseInt(process.env['BCRYPT_ROUNDS'] || '12', 10),
  },
  upload: {
    path: process.env['UPLOAD_PATH'] || 'uploads',
    maxFileSize: parseInt(process.env['MAX_FILE_SIZE'] || '5242880', 10), // 5MB
    allowedTypes: (process.env['ALLOWED_FILE_TYPES'] || 'jpg,jpeg,png,gif').split(','),
  },
  logging: {
    level: process.env['LOG_LEVEL'] || 'info',
    filePath: process.env['LOG_FILE_PATH'] || 'logs',
  },
  rateLimit: {
    windowMs: parseInt(process.env['RATE_LIMIT_WINDOW_MS'] || '900000', 10), // 15分钟
    maxRequests: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '100', 10),
  },
  cors: {
    origin: process.env['CORS_ORIGIN'] || 'http://localhost:5173',
    credentials: process.env['CORS_CREDENTIALS'] === 'true',
  },
  security: {
    helmetEnabled: process.env['HELMET_ENABLED'] !== 'false',
    compressionEnabled: process.env['COMPRESSION_ENABLED'] !== 'false',
  },
};

// 验证必需的环境变量
const requiredEnvVars = [
  'JWT_SECRET',
  'DB_PASSWORD',
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  throw new Error(`缺少必需的环境变量: ${missingEnvVars.join(', ')}`);
}
