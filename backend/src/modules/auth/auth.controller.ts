/**
 * 认证控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-003 认证系统, 创建认证控制器; Principle_Applied: MVC架构模式;}}
 */

import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';
import { AuthService } from './auth.service';
import { asyncHandler, createApiError } from '@/shared/middleware/error.middleware';
import { logger } from '@/shared/utils/logger';

export class AuthController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  /**
   * 用户登录验证规则
   */
  public loginValidation = [
    body('username')
      .notEmpty()
      .withMessage('用户名不能为空')
      .isLength({ min: 3, max: 50 })
      .withMessage('用户名长度必须在3-50个字符之间')
      .matches(/^[a-zA-Z0-9_]+$/)
      .withMessage('用户名只能包含字母、数字和下划线'),
    
    body('password')
      .notEmpty()
      .withMessage('密码不能为空')
      .isLength({ min: 6, max: 50 })
      .withMessage('密码长度必须在6-50个字符之间'),
  ];

  /**
   * 刷新令牌验证规则
   */
  public refreshTokenValidation = [
    body('refreshToken')
      .notEmpty()
      .withMessage('刷新令牌不能为空'),
  ];

  /**
   * 用户登录
   */
  public login = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createApiError('输入数据验证失败', 400, 'VALIDATION_ERROR', errors.array());
    }

    const { username, password } = req.body;

    try {
      const result = await this.authService.login({ username, password });

      logger.info('用户登录API调用成功', {
        userId: result.user.id,
        username: result.user.username,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '登录成功',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('用户登录API调用失败', {
        username,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 刷新访问令牌
   */
  public refreshToken = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // 检查验证结果
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createApiError('输入数据验证失败', 400, 'VALIDATION_ERROR', errors.array());
    }

    const { refreshToken } = req.body;

    try {
      const result = await this.authService.refreshToken({ refreshToken });

      logger.info('令牌刷新API调用成功', {
        ip: req.ip,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '令牌刷新成功',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('令牌刷新API调用失败', {
        ip: req.ip,
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 获取当前用户信息
   */
  public getProfile = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
    }

    try {
      const result = await this.authService.getUserProfile(req.user.userId);

      logger.info('获取用户信息API调用成功', {
        userId: req.user.userId,
        username: req.user.username,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '获取用户信息成功',
        data: result,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('获取用户信息API调用失败', {
        userId: req.user?.userId,
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 用户登出
   */
  public logout = asyncHandler(async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      throw createApiError('用户未认证', 401, 'USER_NOT_AUTHENTICATED');
    }

    try {
      await this.authService.logout(req.user.userId);

      logger.info('用户登出API调用成功', {
        userId: req.user.userId,
        username: req.user.username,
        requestId: req.requestId,
      });

      res.status(200).json({
        code: 200,
        message: '登出成功',
        data: null,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('用户登出API调用失败', {
        userId: req.user?.userId,
        requestId: req.requestId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      next(error);
    }
  });

  /**
   * 验证令牌有效性
   */
  public verifyToken = asyncHandler(async (req: Request, res: Response): Promise<void> => {
    if (!req.user) {
      throw createApiError('令牌无效', 401, 'INVALID_TOKEN');
    }

    res.status(200).json({
      code: 200,
      message: '令牌有效',
      data: {
        valid: true,
        user: {
          id: req.user.userId,
          username: req.user.username,
          enterpriseId: req.user.enterpriseId,
          roles: req.user.roles,
          permissions: req.user.permissions,
        },
      },
      timestamp: new Date().toISOString(),
    });
  });
}
