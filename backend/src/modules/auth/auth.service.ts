/**
 * 认证服务
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-003 认证系统, 创建认证服务; Principle_Applied: 业务逻辑分离;}}
 */

import { User, Role, Permission } from '@/shared/database/models';
import { generateAccessToken, generateRefreshToken, verifyRefreshToken } from '@/shared/utils/jwt';
import { createApiError } from '@/shared/middleware/error.middleware';
import { logger } from '@/shared/utils/logger';

// 登录请求接口
export interface LoginRequest {
  username: string;
  password: string;
}

// 登录响应接口
export interface LoginResponse {
  user: {
    id: number;
    username: string;
    realName: string;
    email?: string;
    phone?: string;
    avatarUrl?: string;
    enterpriseId: number;
    departmentId?: number;
    status: number;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: string;
  };
  permissions: string[];
  roles: string[];
}

// 刷新令牌请求接口
export interface RefreshTokenRequest {
  refreshToken: string;
}

// 用户信息响应接口
export interface UserProfileResponse {
  id: number;
  username: string;
  realName: string;
  email?: string;
  phone?: string;
  avatarUrl?: string;
  enterpriseId: number;
  departmentId?: number;
  status: number;
  enterprise?: any;
  department?: any;
  roles: string[];
  permissions: string[];
  lastLoginAt?: Date;
  createdAt: Date;
}

export class AuthService {
  /**
   * 用户登录
   */
  async login(loginData: LoginRequest): Promise<LoginResponse> {
    const { username, password } = loginData;

    try {
      // 查找用户（包含密码）
      const user = await User.scope('withPassword').findOne({
        where: { username, status: 1 },
        include: [
          {
            model: Role,
            as: 'roles',
            include: [
              {
                model: Permission,
                as: 'permissions',
                attributes: ['code'],
              },
            ],
          },
        ],
      });

      if (!user) {
        throw createApiError('用户名或密码错误', 401, 'INVALID_CREDENTIALS');
      }

      // 验证密码
      const isPasswordValid = await user.validatePassword(password);
      if (!isPasswordValid) {
        throw createApiError('用户名或密码错误', 401, 'INVALID_CREDENTIALS');
      }

      // 获取用户角色和权限
      const roles = user.roles?.map(role => role.code) || [];
      const permissions = this.extractPermissions(user.roles || []);

      // 生成令牌
      const accessToken = generateAccessToken({
        userId: user.id,
        username: user.username,
        enterpriseId: user.enterpriseId,
        roles,
        permissions,
      });

      const refreshToken = generateRefreshToken({
        userId: user.id,
        username: user.username,
        tokenVersion: 1, // 可用于令牌版本控制
      });

      // 更新最后登录时间
      await user.updateLastLogin();

      // 记录登录日志
      logger.info('用户登录成功', {
        userId: user.id,
        username: user.username,
        enterpriseId: user.enterpriseId,
        roles,
        permissions: permissions.length,
      });

      return {
        user: {
          id: user.id,
          username: user.username,
          realName: user.realName,
          ...(user.email && { email: user.email }),
          ...(user.phone && { phone: user.phone }),
          ...(user.avatarUrl && { avatarUrl: user.avatarUrl }),
          enterpriseId: user.enterpriseId,
          ...(user.departmentId && { departmentId: user.departmentId }),
          status: user.status,
        },
        tokens: {
          accessToken,
          refreshToken,
          expiresIn: '24h',
        },
        permissions,
        roles,
      };
    } catch (error) {
      logger.error('用户登录失败', {
        username,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken(refreshData: RefreshTokenRequest): Promise<{ accessToken: string; expiresIn: string }> {
    const { refreshToken } = refreshData;

    try {
      // 验证刷新令牌
      const payload = verifyRefreshToken(refreshToken);

      // 查找用户并获取最新的角色权限信息
      const user = await User.findOne({
        where: { id: payload.userId, status: 1 },
        include: [
          {
            model: Role,
            as: 'roles',
            include: [
              {
                model: Permission,
                as: 'permissions',
                attributes: ['code'],
              },
            ],
          },
        ],
      });

      if (!user) {
        throw createApiError('用户不存在或已被禁用', 401, 'USER_NOT_FOUND');
      }

      // 获取最新的角色和权限
      const roles = user.roles?.map(role => role.code) || [];
      const permissions = this.extractPermissions(user.roles || []);

      // 生成新的访问令牌
      const accessToken = generateAccessToken({
        userId: user.id,
        username: user.username,
        enterpriseId: user.enterpriseId,
        roles,
        permissions,
      });

      logger.info('令牌刷新成功', {
        userId: user.id,
        username: user.username,
      });

      return {
        accessToken,
        expiresIn: '24h',
      };
    } catch (error) {
      logger.error('令牌刷新失败', {
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  }

  /**
   * 获取用户信息
   */
  async getUserProfile(userId: number): Promise<UserProfileResponse> {
    try {
      const user = await User.findOne({
        where: { id: userId },
        include: [
          {
            model: Role,
            as: 'roles',
            include: [
              {
                model: Permission,
                as: 'permissions',
                attributes: ['code'],
              },
            ],
          },
          {
            association: 'enterprise',
            attributes: ['id', 'name', 'code'],
          },
          {
            association: 'department',
            attributes: ['id', 'name'],
          },
        ],
      });

      if (!user) {
        throw createApiError('用户不存在', 404, 'USER_NOT_FOUND');
      }

      const roles = user.roles?.map(role => role.code) || [];
      const permissions = this.extractPermissions(user.roles || []);

      return {
        id: user.id,
        username: user.username,
        realName: user.realName,
        ...(user.email && { email: user.email }),
        ...(user.phone && { phone: user.phone }),
        ...(user.avatarUrl && { avatarUrl: user.avatarUrl }),
        enterpriseId: user.enterpriseId,
        ...(user.departmentId && { departmentId: user.departmentId }),
        status: user.status,
        enterprise: user.enterprise,
        department: user.department,
        roles,
        permissions,
        ...(user.lastLoginAt && { lastLoginAt: user.lastLoginAt }),
        createdAt: user.createdAt,
      };
    } catch (error) {
      logger.error('获取用户信息失败', {
        userId,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  }

  /**
   * 用户登出（可选实现，主要用于记录日志）
   */
  async logout(userId: number): Promise<void> {
    try {
      logger.info('用户登出', { userId });
      // 这里可以实现令牌黑名单等功能
    } catch (error) {
      logger.error('用户登出失败', {
        userId,
        error: error instanceof Error ? error.message : '未知错误',
      });
    }
  }

  /**
   * 从角色中提取权限
   */
  private extractPermissions(roles: any[]): string[] {
    const permissionSet = new Set<string>();
    
    roles.forEach(role => {
      if (role.permissions) {
        role.permissions.forEach((permission: any) => {
          permissionSet.add(permission.code);
        });
      }
    });

    return Array.from(permissionSet);
  }
}
