/**
 * 认证路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-003 认证系统, 创建认证路由; Principle_Applied: RESTful API设计;}}
 */

import { Router } from 'express';
import { AuthController } from './auth.controller';
import { authMiddleware } from '@/shared/middleware/auth.middleware';
import { loginRateLimitMiddleware } from '@/shared/middleware/rate-limit.middleware';

const router = Router();
const authController = new AuthController();

/**
 * @route   POST /auth/login
 * @desc    用户登录
 * @access  Public
 */
router.post(
  '/login',
  loginRateLimitMiddleware, // 登录限流
  authController.loginValidation, // 输入验证
  authController.login
);

/**
 * @route   POST /auth/refresh
 * @desc    刷新访问令牌
 * @access  Public
 */
router.post(
  '/refresh',
  authController.refreshTokenValidation, // 输入验证
  authController.refreshToken
);

/**
 * @route   GET /auth/profile
 * @desc    获取当前用户信息
 * @access  Private
 */
router.get(
  '/profile',
  authMiddleware, // 需要认证
  authController.getProfile
);

/**
 * @route   POST /auth/logout
 * @desc    用户登出
 * @access  Private
 */
router.post(
  '/logout',
  authMiddleware, // 需要认证
  authController.logout
);

/**
 * @route   GET /auth/verify
 * @desc    验证令牌有效性
 * @access  Private
 */
router.get(
  '/verify',
  authMiddleware, // 需要认证
  authController.verifyToken
);

export { router as authRoutes };
