/**
 * 刺绣管理系统后端应用入口
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-001 项目初始化, 创建Express应用入口; Principle_Applied: 模块化架构设计;}}
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import dotenv from 'dotenv';
import { createServer } from 'http';

import { config } from '@/config/app.config';
import { logger } from '@/shared/utils/logger';
import { errorHandler } from '@/shared/middleware/error.middleware';
import { notFoundHandler } from '@/shared/middleware/not-found.middleware';
import { rateLimitMiddleware } from '@/shared/middleware/rate-limit.middleware';
import { requestLoggerMiddleware } from '@/shared/middleware/request-logger.middleware';
import { apiRoutes } from '@/shared/routes';
import { testConnection, syncDatabase } from '@/shared/database';
import { initializeAssociations } from '@/shared/database/models';
import { runAllSeeders } from '@/shared/database/seeders';

// 加载环境变量
dotenv.config();

class Application {
  public app: express.Application;
  public server: any;

  constructor() {
    this.app = express();
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  /**
   * 初始化中间件
   */
  private initializeMiddlewares(): void {
    // 安全中间件
    if (config.security.helmetEnabled) {
      this.app.use(helmet());
    }

    // CORS配置
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: config.cors.credentials,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Request-ID'],
    }));

    // 压缩中间件
    if (config.security.compressionEnabled) {
      this.app.use(compression());
    }

    // 请求日志
    this.app.use(morgan('combined', {
      stream: { write: (message: string) => logger.info(message.trim()) },
    }));

    // 请求解析
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 限流中间件
    this.app.use(rateLimitMiddleware);

    // 自定义请求日志中间件
    this.app.use(requestLoggerMiddleware);

    // 健康检查
    this.app.get('/health', (_req, res) => {
      res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.app.nodeEnv,
      });
    });
  }

  /**
   * 初始化路由
   */
  private initializeRoutes(): void {
    // API路由
    this.app.use(config.app.apiPrefix, apiRoutes);
  }

  /**
   * 初始化错误处理
   */
  private initializeErrorHandling(): void {
    // 404处理
    this.app.use(notFoundHandler);

    // 全局错误处理
    this.app.use(errorHandler);
  }

  /**
   * 初始化数据库
   */
  private async initializeDatabase(): Promise<void> {
    try {
      // 测试数据库连接
      await testConnection();

      // 初始化模型关联
      initializeAssociations();

      // 同步数据库结构
      await syncDatabase(false); // 生产环境不要使用force: true

      // 运行种子数据
      if (config.app.nodeEnv === 'development') {
        await runAllSeeders();
      }

      logger.info('数据库初始化完成');
    } catch (error) {
      logger.error('数据库初始化失败', error);
      throw error;
    }
  }

  /**
   * 启动服务器
   */
  public async listen(): Promise<void> {
    try {
      // 初始化数据库
      await this.initializeDatabase();

      this.server = createServer(this.app);

      this.server.listen(config.app.port, () => {
        logger.info(`🚀 服务器启动成功`);
        logger.info(`📍 端口: ${config.app.port}`);
        logger.info(`🌍 环境: ${config.app.nodeEnv}`);
        logger.info(`📡 API前缀: ${config.app.apiPrefix}`);
        logger.info(`🔗 健康检查: http://localhost:${config.app.port}/health`);
        logger.info(`💾 数据库: ${config.database.host}:${config.database.port}/${config.database.name}`);
      });

      // 优雅关闭
      process.on('SIGTERM', this.gracefulShutdown.bind(this));
      process.on('SIGINT', this.gracefulShutdown.bind(this));
    } catch (error) {
      logger.error('服务器启动失败', error);
      process.exit(1);
    }
  }

  /**
   * 优雅关闭服务器
   */
  private gracefulShutdown(signal: string): void {
    logger.info(`收到 ${signal} 信号，开始优雅关闭服务器...`);
    
    this.server.close(() => {
      logger.info('服务器已关闭');
      process.exit(0);
    });

    // 强制关闭超时
    setTimeout(() => {
      logger.error('强制关闭服务器');
      process.exit(1);
    }, 10000);
  }
}

// 启动应用
const application = new Application();

if (require.main === module) {
  application.listen();
}

export { application };
export default application.app;
