// 调试脚本 - 检查后端启动问题
const { config } = require('./dist/config/app.config.js');

console.log('=== 环境变量检查 ===');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PORT:', process.env.PORT);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : 'undefined');
console.log('JWT_SECRET:', process.env.JWT_SECRET ? '***' : 'undefined');

console.log('\n=== 配置对象检查 ===');
try {
  console.log('App Config:', {
    nodeEnv: config.app.nodeEnv,
    port: config.app.port,
    apiPrefix: config.app.apiPrefix
  });
  
  console.log('Database Config:', {
    host: config.database.host,
    port: config.database.port,
    name: config.database.name,
    username: config.database.username,
    password: config.database.password ? '***' : 'undefined'
  });
} catch (error) {
  console.error('配置加载失败:', error.message);
}

console.log('\n=== 数据库连接测试 ===');
const { Sequelize } = require('sequelize');

const sequelize = new Sequelize({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  database: process.env.DB_NAME || 'embroidery_management',
  username: process.env.DB_USER || 'admin',
  password: process.env.DB_PASSWORD || '123456',
  dialect: 'postgres',
  logging: false
});

sequelize.authenticate()
  .then(() => {
    console.log('✅ 数据库连接成功');
    return sequelize.close();
  })
  .catch(error => {
    console.error('❌ 数据库连接失败:', error.message);
  })
  .finally(() => {
    process.exit(0);
  });
