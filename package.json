{"name": "embroidery-management", "version": "1.0.0", "description": "刺绣管理系统 - 现代化的工业刺绣设备管理平台", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm run preview", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm run test:unit", "test:e2e": "cd frontend && npm run test:e2e", "test:coverage": "npm run test:coverage:backend && npm run test:coverage:frontend", "test:coverage:backend": "cd backend && npm run test:coverage", "test:coverage:frontend": "cd frontend && npm run test:coverage", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "npm run lint:fix:backend && npm run lint:fix:frontend", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint", "install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "clean": "npm run clean:backend && npm run clean:frontend", "clean:backend": "cd backend && rm -rf node_modules dist", "clean:frontend": "cd frontend && rm -rf node_modules dist", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset", "type-check": "npm run type-check:backend && npm run type-check:frontend", "type-check:backend": "cd backend && npx tsc --noEmit", "type-check:frontend": "cd frontend && npm run type-check", "setup": "npm run install:all && npm run setup:env", "setup:env": "node scripts/setup-env.js", "health": "curl -f http://localhost:3002/health && curl -f http://localhost:5173", "logs": "concurrently \"npm run logs:backend\" \"npm run logs:frontend\"", "logs:backend": "cd backend && tail -f logs/combined-$(date +%Y-%m-%d).log", "logs:frontend": "echo 'Frontend logs are in browser console'", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "keywords": ["embroidery", "management", "system", "vue3", "typescript", "nodejs", "express", "postgresql"], "author": "Embroidery Management Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/embroidery-management.git"}, "bugs": {"url": "https://github.com/your-org/embroidery-management/issues"}, "homepage": "https://github.com/your-org/embroidery-management#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "workspaces": ["backend", "frontend"]}