/**
 * 环境配置脚本
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: 主目录package.json, 创建环境配置脚本; Principle_Applied: 自动化环境配置;}}
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupEnvironment() {
  console.log('🔧 开始配置环境变量...\n');

  // 检查后端环境文件
  const backendEnvPath = path.join(__dirname, '../backend/.env');
  const backendEnvExamplePath = path.join(__dirname, '../backend/.env.example');

  if (!fs.existsSync(backendEnvPath)) {
    if (fs.existsSync(backendEnvExamplePath)) {
      console.log('📋 配置后端环境变量...');
      
      // 读取示例文件
      const envExample = fs.readFileSync(backendEnvExamplePath, 'utf8');
      let envContent = envExample;

      // 交互式配置数据库
      console.log('\n数据库配置:');
      const dbHost = await question('数据库主机 (默认: localhost): ') || 'localhost';
      const dbPort = await question('数据库端口 (默认: 5432): ') || '5432';
      const dbName = await question('数据库名称 (默认: embroidery_management): ') || 'embroidery_management';
      const dbUser = await question('数据库用户名 (默认: postgres): ') || 'postgres';
      const dbPassword = await question('数据库密码: ');

      // 替换配置
      envContent = envContent.replace('DB_HOST=localhost', `DB_HOST=${dbHost}`);
      envContent = envContent.replace('DB_PORT=5432', `DB_PORT=${dbPort}`);
      envContent = envContent.replace('DB_NAME=embroidery_management', `DB_NAME=${dbName}`);
      envContent = envContent.replace('DB_USERNAME=postgres', `DB_USERNAME=${dbUser}`);
      envContent = envContent.replace('DB_PASSWORD=your_password', `DB_PASSWORD=${dbPassword}`);

      // 生成JWT密钥
      const jwtSecret = require('crypto').randomBytes(64).toString('hex');
      const refreshSecret = require('crypto').randomBytes(64).toString('hex');
      envContent = envContent.replace('JWT_SECRET=your_super_secret_jwt_key_here_change_in_production', `JWT_SECRET=${jwtSecret}`);
      envContent = envContent.replace('JWT_REFRESH_SECRET=your_refresh_token_secret_here', `JWT_REFRESH_SECRET=${refreshSecret}`);

      // 写入文件
      fs.writeFileSync(backendEnvPath, envContent);
      console.log('✅ 后端环境变量配置完成');
    } else {
      console.log('❌ 未找到后端环境变量示例文件');
    }
  } else {
    console.log('ℹ️  后端环境变量文件已存在');
  }

  // 检查前端环境文件
  const frontendEnvPath = path.join(__dirname, '../frontend/.env');
  const frontendEnvDevPath = path.join(__dirname, '../frontend/.env.development');

  if (!fs.existsSync(frontendEnvPath)) {
    if (fs.existsSync(frontendEnvDevPath)) {
      fs.copyFileSync(frontendEnvDevPath, frontendEnvPath);
      console.log('✅ 前端环境变量配置完成');
    } else {
      console.log('❌ 未找到前端环境变量示例文件');
    }
  } else {
    console.log('ℹ️  前端环境变量文件已存在');
  }

  console.log('\n🎉 环境配置完成！');
  console.log('\n📋 下一步操作:');
  console.log('1. 确保PostgreSQL服务已启动');
  console.log(`2. 创建数据库: createdb ${dbName || 'embroidery_management'}`);
  console.log('3. 运行 npm run dev 启动开发服务器');

  rl.close();
}

// 检查Node.js版本
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 18) {
  console.error('❌ Node.js版本过低，需要18+版本，当前版本：' + nodeVersion);
  process.exit(1);
}

setupEnvironment().catch(console.error);
