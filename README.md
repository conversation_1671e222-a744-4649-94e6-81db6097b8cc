# 刺绣管理系统 (Embroidery Management System)

<!-- {{CHENGQI: Action: Modified; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-001 项目初始化, 更新项目文档; Principle_Applied: 文档规范化;}} -->

## 📋 项目介绍

一个现代化的工业刺绣设备管理系统，采用前后端分离架构，支持多企业、多部门、多用户的复杂权限管理体系。

### 🎯 核心特性

- **三级权限体系**：超级管理员 → 企业管理员 → 普通用户
- **多级部门管理**：支持树形结构的部门组织架构
- **基于职位的权限控制**：细粒度的模块访问权限管理
- **现代化技术栈**：Vue 3 + TypeScript + Ant Design Vue + Express.js + PostgreSQL
- **响应式设计**：支持不同屏幕尺寸的设备访问

## 🏗️ 技术架构

### 前端技术栈
- **框架**：Vue 3 + TypeScript + Vite
- **UI组件库**：Ant Design Vue
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **构建工具**：Vite

### 后端技术栈
- **运行环境**：Node.js + Express.js + TypeScript
- **数据库**：PostgreSQL + Sequelize ORM
- **认证授权**：JWT + bcrypt
- **日志管理**：Winston
- **API文档**：待集成 Swagger

### 项目结构
```
embroidery-management/
├── frontend/          # 前端应用
│   ├── src/
│   │   ├── components/    # 公共组件
│   │   ├── views/         # 页面组件
│   │   ├── stores/        # 状态管理
│   │   ├── api/           # API接口
│   │   └── utils/         # 工具函数
│   └── package.json
├── backend/           # 后端API服务
│   ├── src/
│   │   ├── modules/       # 业务模块
│   │   ├── shared/        # 共享模块
│   │   └── config/        # 配置文件
│   └── package.json
├── project_document/  # 项目文档
└── README.md
```

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- PostgreSQL >= 13.0
- npm >= 8.0.0

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd embroidery-management
   ```

2. **一键安装**
   ```bash
   # 运行安装脚本（推荐）
   ./install.sh

   # 或手动安装
   npm run install:all
   ```

3. **环境配置**
   ```bash
   # 交互式配置（推荐）
   npm run setup

   # 或手动配置
   cp backend/.env.example backend/.env
   # 编辑 backend/.env 文件，配置数据库连接等信息
   ```

4. **数据库初始化**
   ```bash
   # 创建数据库
   createdb embroidery_management

   # 运行迁移和种子数据
   npm run db:migrate
   npm run db:seed
   ```

5. **启动开发服务**
   ```bash
   # 同时启动前后端（推荐）
   npm run dev

   # 或分别启动
   npm run dev:backend   # 后端: http://localhost:3002
   npm run dev:frontend  # 前端: http://localhost:5175
   ```

6. **访问应用**
   - 前端应用：http://localhost:5173
   - 后端API：http://localhost:3002
   - 健康检查：http://localhost:3002/health

## 📚 开发指南

### 常用命令
```bash
# 开发
npm run dev                 # 启动开发服务器
npm run build              # 构建生产版本
npm test                   # 运行测试
npm run lint               # 代码检查

# 数据库
npm run db:migrate         # 运行数据库迁移
npm run db:seed           # 运行种子数据
npm run db:reset          # 重置数据库

# Docker部署
npm run docker:build      # 构建Docker镜像
npm run docker:up         # 启动Docker服务
npm run docker:logs       # 查看Docker日志

# 工具
npm run setup             # 环境配置
npm run clean             # 清理依赖
npm run health            # 健康检查
```

### 代码规范
- 使用 ESLint + Prettier 进行代码格式化
- 遵循 TypeScript 严格模式
- 使用 Conventional Commits 规范提交信息

### 测试策略
- 单元测试：Vitest (前端) + Jest (后端)
- E2E测试：Playwright
- 测试覆盖率目标：≥80%

### 部署说明
- 开发环境：本地开发服务器
- 生产环境：Docker容器化部署
- 详细说明：查看 [DEVELOPMENT.md](DEVELOPMENT.md)

## 📖 项目文档

详细的项目文档位于 `project_document/` 目录：

- [架构设计文档](project_document/architecture/final_arch_v1.0.md)
- [测试策略文档](project_document/tests/test_strategy.md)
- [任务执行记录](project_document/task_file.md)

## 🤝 参与贡献

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 [木兰宽松许可证，第2版](LICENSE) 开源协议。

## 👥 开发团队

- **项目架构**：AI系统架构师
- **开发实施**：AI开发工程师
- **文档维护**：AI文档工程师

---

**当前版本**：v1.0.0
**最后更新**：2025-06-27
