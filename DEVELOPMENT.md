# 开发指南

## 📋 目录
- [快速开始](#快速开始)
- [项目结构](#项目结构)
- [开发命令](#开发命令)
- [环境配置](#环境配置)
- [数据库管理](#数据库管理)
- [测试指南](#测试指南)
- [部署指南](#部署指南)
- [代码规范](#代码规范)

## 🚀 快速开始

### 1. 环境要求
- Node.js >= 18.0.0
- PostgreSQL >= 13.0
- npm >= 8.0.0

### 2. 安装依赖
```bash
# 运行安装脚本
./install.sh

# 或手动安装
npm run install:all
```

### 3. 环境配置
```bash
# 交互式配置环境变量
npm run setup

# 手动配置
cp backend/.env.example backend/.env
cp frontend/.env.development frontend/.env
# 编辑 backend/.env 文件配置数据库连接
```

### 4. 数据库初始化
```bash
# 创建数据库
createdb embroidery_management

# 运行数据库迁移和种子数据
npm run db:migrate
npm run db:seed
```

### 5. 启动开发服务器
```bash
# 同时启动前后端
npm run dev

# 分别启动
npm run dev:backend  # 后端: http://localhost:3002
npm run dev:frontend # 前端: http://localhost:5173
```

## 📁 项目结构

```
embroidery-management/
├── backend/                 # 后端API服务
│   ├── src/
│   │   ├── modules/        # 业务模块
│   │   ├── shared/         # 共享模块
│   │   └── config/         # 配置文件
│   ├── package.json
│   └── Dockerfile
├── frontend/               # 前端Vue应用
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面
│   │   ├── stores/         # 状态管理
│   │   ├── api/            # API接口
│   │   └── utils/          # 工具函数
│   ├── package.json
│   └── Dockerfile
├── project_document/       # 项目文档
├── scripts/               # 脚本文件
├── package.json           # 主项目配置
├── docker-compose.yml     # Docker编排
└── README.md
```

## 🛠️ 开发命令

### 主要命令
```bash
# 开发
npm run dev                 # 启动开发服务器
npm run build              # 构建生产版本
npm run start              # 启动生产服务器

# 测试
npm test                   # 运行所有测试
npm run test:coverage      # 运行测试并生成覆盖率报告
npm run test:e2e          # 运行E2E测试

# 代码质量
npm run lint              # 代码检查
npm run lint:fix          # 自动修复代码问题
npm run type-check        # TypeScript类型检查

# 数据库
npm run db:migrate        # 运行数据库迁移
npm run db:seed          # 运行种子数据
npm run db:reset         # 重置数据库

# Docker
npm run docker:build     # 构建Docker镜像
npm run docker:up        # 启动Docker服务
npm run docker:down      # 停止Docker服务
npm run docker:logs      # 查看Docker日志

# 工具
npm run clean            # 清理依赖和构建文件
npm run health           # 健康检查
npm run logs             # 查看日志
```

### 分模块命令
```bash
# 后端
npm run dev:backend
npm run build:backend
npm run test:backend
npm run lint:backend

# 前端
npm run dev:frontend
npm run build:frontend
npm run test:frontend
npm run lint:frontend
```

## ⚙️ 环境配置

### 后端环境变量 (.env)
```bash
# 应用配置
NODE_ENV=development
PORT=3002
API_PREFIX=/api/v1

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=embroidery_management
DB_USERNAME=postgres
DB_PASSWORD=your_password

# JWT配置
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your_refresh_secret
JWT_REFRESH_EXPIRES_IN=7d

# 其他配置...
```

### 前端环境变量 (.env)
```bash
VITE_API_BASE_URL=http://localhost:3002/api/v1
VITE_APP_TITLE=刺绣管理系统
VITE_APP_VERSION=1.0.0
```

## 🗄️ 数据库管理

### 数据库操作
```bash
# 创建数据库
createdb embroidery_management

# 连接数据库
psql embroidery_management

# 备份数据库
pg_dump embroidery_management > backup.sql

# 恢复数据库
psql embroidery_management < backup.sql
```

### 数据库迁移
```bash
# 运行迁移
npm run db:migrate

# 回滚迁移
cd backend && npm run db:migrate:undo

# 重置数据库
npm run db:reset
```

## 🧪 测试指南

### 单元测试
```bash
# 运行所有单元测试
npm test

# 运行特定模块测试
cd backend && npm test -- --testPathPattern=auth
cd frontend && npm run test:unit -- --testNamePattern="UserStore"

# 监听模式
cd backend && npm run test:watch
cd frontend && npm run test:unit -- --watch
```

### E2E测试
```bash
# 运行E2E测试
npm run test:e2e

# 调试模式
cd frontend && npx playwright test --debug
```

### 测试覆盖率
```bash
# 生成覆盖率报告
npm run test:coverage

# 查看覆盖率报告
open backend/coverage/lcov-report/index.html
open frontend/coverage/index.html
```

## 🚀 部署指南

### 开发环境
```bash
npm run dev
```

### 生产环境
```bash
# 构建
npm run build

# 启动
npm run start
```

### Docker部署
```bash
# 构建镜像
npm run docker:build

# 启动服务
npm run docker:up

# 查看状态
docker-compose ps

# 查看日志
npm run docker:logs

# 停止服务
npm run docker:down
```

## 📝 代码规范

### TypeScript规范
- 使用严格模式
- 明确的类型定义
- 避免使用any类型
- 使用接口定义数据结构

### Vue组件规范
- 使用Composition API
- 组件名使用PascalCase
- Props使用camelCase
- 事件名使用kebab-case

### API设计规范
- RESTful API设计
- 统一的响应格式
- 完整的错误处理
- API版本控制

### Git提交规范
```bash
# 提交格式
<type>(<scope>): <subject>

# 示例
feat(auth): 添加JWT认证功能
fix(user): 修复用户列表分页问题
docs(readme): 更新安装说明
```

### 代码检查
```bash
# 运行ESLint
npm run lint

# 自动修复
npm run lint:fix

# TypeScript检查
npm run type-check
```

## 🔧 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 查看端口占用
   lsof -i :3000
   lsof -i :5173
   
   # 杀死进程
   kill -9 <PID>
   ```

2. **数据库连接失败**
   - 检查PostgreSQL服务是否启动
   - 验证数据库连接配置
   - 确认数据库是否存在

3. **依赖安装失败**
   ```bash
   # 清理缓存
   npm run clean
   rm -rf node_modules package-lock.json
   npm install
   ```

4. **权限问题**
   ```bash
   # 修复文件权限
   chmod +x install.sh
   chmod +x scripts/*.js
   ```

### 日志查看
```bash
# 应用日志
npm run logs

# Docker日志
npm run docker:logs

# 数据库日志
tail -f /var/log/postgresql/postgresql-*.log
```

## 📞 获取帮助

- 查看项目文档：`project_document/`
- 提交Issue：GitHub Issues
- 开发团队：内部沟通渠道
