# Docker Compose配置文件
# {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: 主目录package.json, 创建Docker配置; Principle_Applied: 容器化部署;}}

version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: embroidery-postgres
    environment:
      POSTGRES_DB: embroidery_management
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: "123456"
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - embroidery-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis缓存（可选）
  redis:
    image: redis:7-alpine
    container_name: embroidery-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - embroidery-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: embroidery-backend
    environment:
      NODE_ENV: production
      PORT: 3002
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: embroidery_management
      DB_USER: admin
      DB_PASSWORD: "123456"
      DB_TABLE_PREFIX: em_
      JWT_SECRET: your_production_jwt_secret_here
      JWT_REFRESH_SECRET: your_production_refresh_secret_here
    ports:
      - "3002:3002"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - embroidery-network
    restart: unless-stopped
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/uploads:/app/uploads
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端Web服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: embroidery-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - embroidery-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: embroidery-nginx
    ports:
      - "8080:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
    depends_on:
      - backend
      - frontend
    networks:
      - embroidery-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  embroidery-network:
    driver: bridge
