#!/bin/bash

# 刺绣管理系统安装脚本
# {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-005 基础布局, 创建安装脚本; Principle_Applied: 自动化部署;}}

echo "🚀 开始安装刺绣管理系统..."

# 检查Node.js版本
echo "📋 检查Node.js版本..."
if ! command -v node &> /dev/null; then
    echo "❌ 未找到Node.js，请先安装Node.js 18+版本"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js版本过低，需要18+版本，当前版本：$(node -v)"
    exit 1
fi

echo "✅ Node.js版本检查通过：$(node -v)"

# 检查PostgreSQL
echo "📋 检查PostgreSQL..."
if ! command -v psql &> /dev/null; then
    echo "⚠️  未找到PostgreSQL，请确保已安装PostgreSQL 13+版本"
    echo "   安装指南：https://www.postgresql.org/download/"
fi

# 安装主目录依赖
echo "📦 安装主目录依赖..."
if [ ! -f package.json ]; then
    echo "❌ 未找到主目录package.json文件"
    exit 1
fi

npm install
if [ $? -ne 0 ]; then
    echo "❌ 主目录依赖安装失败"
    exit 1
fi

echo "✅ 主目录依赖安装完成"

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
if [ ! -f package.json ]; then
    echo "❌ 未找到backend/package.json文件"
    exit 1
fi

npm install
if [ $? -ne 0 ]; then
    echo "❌ 后端依赖安装失败"
    exit 1
fi

echo "✅ 后端依赖安装完成"

# 安装前端依赖
echo "📦 安装前端依赖..."
cd ../frontend
if [ ! -f package.json ]; then
    echo "❌ 未找到frontend/package.json文件"
    exit 1
fi

npm install
if [ $? -ne 0 ]; then
    echo "❌ 前端依赖安装失败"
    exit 1
fi

echo "✅ 前端依赖安装完成"

# 返回根目录
cd ..

# 创建环境变量文件
echo "⚙️  配置环境变量..."

# 后端环境变量
if [ ! -f backend/.env ]; then
    cp backend/.env.example backend/.env
    echo "✅ 已创建backend/.env文件，请根据实际情况修改数据库配置"
else
    echo "ℹ️  backend/.env文件已存在"
fi

# 前端环境变量
if [ ! -f frontend/.env ]; then
    cp frontend/.env.development frontend/.env
    echo "✅ 已创建frontend/.env文件"
else
    echo "ℹ️  frontend/.env文件已存在"
fi

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 下一步操作："
echo "1. 配置环境变量：npm run setup"
echo "2. 创建数据库：createdb embroidery_management"
echo "3. 启动开发服务器：npm run dev"
echo ""
echo "🌐 访问地址："
echo "   前端：http://localhost:5173"
echo "   后端：http://localhost:3002"
echo "   健康检查：http://localhost:3002/health"
echo ""
echo "👤 默认管理员账号："
echo "   用户名：admin"
echo "   密码：admin123"
echo ""
echo "🐳 Docker部署："
echo "   构建镜像：npm run docker:build"
echo "   启动服务：npm run docker:up"
echo "   查看日志：npm run docker:logs"
echo ""
echo "📚 更多信息请查看 README.md 文件"
