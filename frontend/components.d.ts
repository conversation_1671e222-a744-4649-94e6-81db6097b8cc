/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AButton: typeof import('ant-design-vue/es')['Button']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputPassword: typeof import('ant-design-vue/es')['InputPassword']
    BreadcrumbNav: typeof import('./src/components/Layout/BreadcrumbNav.vue')['default']
    MainLayout: typeof import('./src/components/Layout/MainLayout.vue')['default']
    PermissionWrapper: typeof import('./src/components/Permission/PermissionWrapper.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SideMenu: typeof import('./src/components/Layout/SideMenu.vue')['default']
  }
}
