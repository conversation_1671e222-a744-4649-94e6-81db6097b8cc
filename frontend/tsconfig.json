{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/views/*": ["./src/views/*"], "@/stores/*": ["./src/stores/*"], "@/api/*": ["./src/api/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/assets/*": ["./src/assets/*"]}, "types": ["vite/client", "node"], "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "skipLibCheck": true}}