/**
 * 权限指令
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-005 基础布局, 创建权限指令; Principle_Applied: 指令式权限控制;}}
 */

import type { App, DirectiveBinding } from 'vue'
import { useUserStore } from '@/stores/user'

interface PermissionBinding {
  value: string | string[] | {
    permissions?: string | string[]
    roles?: string | string[]
    mode?: 'all' | 'any' // all: 需要所有权限, any: 需要任意权限
  }
}

/**
 * 权限检查函数
 */
function checkPermission(binding: DirectiveBinding<PermissionBinding['value']>): boolean {
  const userStore = useUserStore()
  
  if (!userStore.isAuthenticated) {
    return false
  }
  
  // 超级管理员拥有所有权限
  if (userStore.isSuperAdmin) {
    return true
  }
  
  const { value } = binding
  
  if (!value) {
    return true
  }
  
  // 如果是字符串或字符串数组，视为权限检查
  if (typeof value === 'string') {
    return userStore.hasPermission(value)
  }
  
  if (Array.isArray(value)) {
    return userStore.hasPermissions(value)
  }
  
  // 如果是对象，支持更复杂的权限检查
  if (typeof value === 'object') {
    const { permissions, roles, mode = 'all' } = value
    
    let hasPermission = true
    let hasRole = true
    
    // 检查权限
    if (permissions) {
      if (typeof permissions === 'string') {
        hasPermission = userStore.hasPermission(permissions)
      } else if (Array.isArray(permissions)) {
        if (mode === 'all') {
          hasPermission = userStore.hasPermissions(permissions)
        } else {
          hasPermission = userStore.hasAnyPermission(permissions)
        }
      }
    }
    
    // 检查角色
    if (roles) {
      if (typeof roles === 'string') {
        hasRole = userStore.hasRole(roles)
      } else if (Array.isArray(roles)) {
        if (mode === 'all') {
          hasRole = userStore.hasRoles(roles)
        } else {
          hasRole = userStore.hasRoles(roles) // hasRoles 本身就是 any 模式
        }
      }
    }
    
    return hasPermission && hasRole
  }
  
  return false
}

/**
 * v-permission 指令
 * 用法：
 * v-permission="'user:create'" - 检查单个权限
 * v-permission="['user:create', 'user:update']" - 检查多个权限（需要全部拥有）
 * v-permission="{ permissions: ['user:create'], roles: ['ADMIN'], mode: 'any' }" - 复杂权限检查
 */
export const permissionDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding<PermissionBinding['value']>) {
    const hasPermission = checkPermission(binding)
    if (!hasPermission) {
      el.style.display = 'none'
    }
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding<PermissionBinding['value']>) {
    const hasPermission = checkPermission(binding)
    if (hasPermission) {
      el.style.display = ''
    } else {
      el.style.display = 'none'
    }
  }
}

/**
 * v-permission-disabled 指令
 * 用法同 v-permission，但是没有权限时禁用元素而不是隐藏
 */
export const permissionDisabledDirective = {
  mounted(el: HTMLElement, binding: DirectiveBinding<PermissionBinding['value']>) {
    const hasPermission = checkPermission(binding)
    if (!hasPermission) {
      el.setAttribute('disabled', 'true')
      el.style.opacity = '0.5'
      el.style.cursor = 'not-allowed'
      el.style.pointerEvents = 'none'
    }
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding<PermissionBinding['value']>) {
    const hasPermission = checkPermission(binding)
    if (hasPermission) {
      el.removeAttribute('disabled')
      el.style.opacity = ''
      el.style.cursor = ''
      el.style.pointerEvents = ''
    } else {
      el.setAttribute('disabled', 'true')
      el.style.opacity = '0.5'
      el.style.cursor = 'not-allowed'
      el.style.pointerEvents = 'none'
    }
  }
}

/**
 * 注册权限指令
 */
export function setupPermissionDirectives(app: App) {
  app.directive('permission', permissionDirective)
  app.directive('permission-disabled', permissionDisabledDirective)
}
