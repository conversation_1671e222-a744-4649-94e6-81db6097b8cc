/**
 * 认证相关API
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建认证API; Principle_Applied: API接口封装;}}
 */

import { http } from './request'
import type { 
  LoginRequest, 
  LoginResponse, 
  RefreshTokenRequest, 
  RefreshTokenResponse, 
  UserProfile 
} from '@/types/auth'

export const authApi = {
  /**
   * 用户登录
   */
  login: (data: LoginRequest): Promise<LoginResponse> => {
    return http.post('/auth/login', data)
  },

  /**
   * 用户登出
   */
  logout: (): Promise<void> => {
    return http.post('/auth/logout')
  },

  /**
   * 刷新访问令牌
   */
  refreshToken: (data: RefreshTokenRequest): Promise<RefreshTokenResponse> => {
    return http.post('/auth/refresh', data)
  },

  /**
   * 获取当前用户信息
   */
  getProfile: (): Promise<UserProfile> => {
    return http.get('/auth/profile')
  },

  /**
   * 验证令牌有效性
   */
  verifyToken: (): Promise<{ valid: boolean; user: any }> => {
    return http.get('/auth/verify')
  }
}
