/**
 * 认证相关类型定义
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建类型定义; Principle_Applied: TypeScript类型安全;}}
 */

// 登录请求
export interface LoginRequest {
  username: string
  password: string
}

// 登录响应
export interface LoginResponse {
  user: UserProfile
  tokens: {
    accessToken: string
    refreshToken: string
    expiresIn: string
  }
  permissions: string[]
  roles: string[]
}

// 用户信息
export interface UserProfile {
  id: number
  username: string
  realName: string
  email?: string
  phone?: string
  avatarUrl?: string
  enterpriseId: number
  departmentId?: number
  status: number
  enterprise?: {
    id: number
    name: string
    code: string
  }
  department?: {
    id: number
    name: string
  }
  roles: string[]
  permissions: string[]
  lastLoginAt?: string
  createdAt: string
}

// 刷新令牌请求
export interface RefreshTokenRequest {
  refreshToken: string
}

// 刷新令牌响应
export interface RefreshTokenResponse {
  accessToken: string
  expiresIn: string
}

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp: string
}

// 分页响应
export interface PageResponse<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

// 企业信息
export interface Enterprise {
  id: number
  name: string
  code: string
  logoUrl?: string
  description?: string
  status: number
  createdAt: string
  updatedAt: string
}

// 部门信息
export interface Department {
  id: number
  enterpriseId: number
  name: string
  parentId?: number
  description?: string
  level: number
  path: string
  sortOrder: number
  children?: Department[]
  createdAt: string
  updatedAt: string
}

// 角色信息
export interface Role {
  id: number
  enterpriseId?: number
  name: string
  code: string
  description?: string
  isSystem: boolean
  permissions?: Permission[]
  createdAt: string
  updatedAt: string
}

// 权限信息
export interface Permission {
  id: number
  name: string
  code: string
  module: string
  description?: string
  createdAt: string
  updatedAt: string
}

// 用户信息（完整）
export interface User {
  id: number
  enterpriseId: number
  departmentId?: number
  username: string
  email?: string
  phone?: string
  realName: string
  avatarUrl?: string
  status: number
  lastLoginAt?: string
  enterprise?: Enterprise
  department?: Department
  roles?: Role[]
  createdAt: string
  updatedAt: string
}
