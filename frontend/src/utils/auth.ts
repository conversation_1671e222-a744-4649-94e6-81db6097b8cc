/**
 * 认证工具函数
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建认证工具; Principle_Applied: 工具函数封装;}}
 */

/**
 * 检查令牌是否过期
 */
export function isTokenExpired(token: string): boolean {
  if (!token) return true
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    return payload.exp < currentTime
  } catch (error) {
    return true
  }
}

/**
 * 获取令牌剩余时间（秒）
 */
export function getTokenRemainingTime(token: string): number {
  if (!token) return 0
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Math.floor(Date.now() / 1000)
    return Math.max(0, payload.exp - currentTime)
  } catch (error) {
    return 0
  }
}

/**
 * 检查令牌是否即将过期（剩余时间少于5分钟）
 */
export function isTokenExpiringSoon(token: string): boolean {
  const remainingTime = getTokenRemainingTime(token)
  return remainingTime > 0 && remainingTime < 300 // 5分钟
}

/**
 * 从令牌中提取用户信息
 */
export function extractUserFromToken(token: string): any {
  if (!token) return null
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return {
      userId: payload.userId,
      username: payload.username,
      enterpriseId: payload.enterpriseId,
      roles: payload.roles || [],
      permissions: payload.permissions || []
    }
  } catch (error) {
    return null
  }
}

/**
 * 格式化用户角色显示名称
 */
export function formatRoleName(roleCode: string): string {
  const roleMap: Record<string, string> = {
    'SUPER_ADMIN': '超级管理员',
    'ENTERPRISE_ADMIN': '企业管理员',
    'SALES': '销售',
    'MERCHANDISER': '跟单',
    'PATTERN_MAKER': '制板师',
    'WORKSHOP_DIRECTOR': '车间主任',
    'TEAM_LEADER': '领班',
    'OPERATOR': '挡车工'
  }
  
  return roleMap[roleCode] || roleCode
}

/**
 * 格式化权限显示名称
 */
export function formatPermissionName(permissionCode: string): string {
  const [module, action] = permissionCode.split(':')
  
  const moduleMap: Record<string, string> = {
    'user': '用户',
    'enterprise': '企业',
    'department': '部门',
    'role': '角色',
    'permission': '权限',
    'order': '订单',
    'production': '生产',
    'inventory': '库存',
    'salary': '工资',
    'digital': '数字空间'
  }
  
  const actionMap: Record<string, string> = {
    'view': '查看',
    'create': '创建',
    'update': '编辑',
    'delete': '删除',
    'manage': '管理',
    'assign': '分配',
    'status': '状态管理'
  }
  
  const moduleName = moduleMap[module] || module
  const actionName = actionMap[action] || action
  
  return `${moduleName}${actionName}`
}

/**
 * 检查用户是否有指定权限
 */
export function hasPermission(userPermissions: string[], requiredPermission: string): boolean {
  return userPermissions.includes(requiredPermission)
}

/**
 * 检查用户是否有指定权限中的任意一个
 */
export function hasAnyPermission(userPermissions: string[], requiredPermissions: string[]): boolean {
  return requiredPermissions.some(permission => userPermissions.includes(permission))
}

/**
 * 检查用户是否有所有指定权限
 */
export function hasAllPermissions(userPermissions: string[], requiredPermissions: string[]): boolean {
  return requiredPermissions.every(permission => userPermissions.includes(permission))
}

/**
 * 检查用户是否有指定角色
 */
export function hasRole(userRoles: string[], requiredRole: string): boolean {
  return userRoles.includes(requiredRole)
}

/**
 * 检查用户是否有指定角色中的任意一个
 */
export function hasAnyRole(userRoles: string[], requiredRoles: string[]): boolean {
  return requiredRoles.some(role => userRoles.includes(role))
}

/**
 * 生成头像URL（如果用户没有头像，生成默认头像）
 */
export function generateAvatarUrl(user: { realName: string; avatarUrl?: string }): string {
  if (user.avatarUrl) {
    return user.avatarUrl
  }
  
  // 生成基于姓名的默认头像
  const firstChar = user.realName.charAt(0).toUpperCase()
  const colors = [
    '#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068',
    '#108ee9', '#f50', '#2db7f5', '#52c41a', '#eb2f96'
  ]
  const colorIndex = user.realName.charCodeAt(0) % colors.length
  const backgroundColor = colors[colorIndex]
  
  // 这里可以返回一个生成头像的URL，或者在组件中使用这些信息
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
      <circle cx="20" cy="20" r="20" fill="${backgroundColor}"/>
      <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-family="Arial">${firstChar}</text>
    </svg>
  `)}`
}
