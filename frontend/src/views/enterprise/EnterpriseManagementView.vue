<!--
  企业管理页面（占位）
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建企业管理占位页面; Principle_Applied: 模块化页面结构;}}
-->

<template>
  <div class="enterprise-management">
    <a-card title="企业管理" :bordered="false">
      <template #extra>
        <a-button type="primary" :disabled="!canCreate">
          <PlusOutlined />
          新增企业
        </a-button>
      </template>
      
      <a-result
        status="info"
        title="企业管理模块"
        sub-title="此模块将在后续任务中实现，包含企业的增删改查、企业信息管理等功能。仅超级管理员可访问。"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="$router.push('/dashboard')">
              返回首页
            </a-button>
          </a-space>
        </template>
      </a-result>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const canCreate = computed(() => userStore.hasPermission('enterprise:create'))
</script>

<style scoped>
.enterprise-management {
  padding: 24px;
}
</style>
