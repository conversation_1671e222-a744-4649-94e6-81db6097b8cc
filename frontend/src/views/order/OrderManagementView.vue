<!--
  订单管理页面（占位）
-->

<template>
  <div class="order-management">
    <a-card title="订单管理" :bordered="false">
      <a-result
        status="info"
        title="订单管理模块"
        sub-title="此模块将在后续任务中实现。"
      >
        <template #extra>
          <a-button type="primary" @click="$router.push('/dashboard')">
            返回首页
          </a-button>
        </template>
      </a-result>
    </a-card>
  </div>
</template>

<script setup lang="ts">
</script>

<style scoped>
.order-management {
  padding: 24px;
}
</style>
