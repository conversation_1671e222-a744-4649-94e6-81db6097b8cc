<!--
  部门管理页面（占位）
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建部门管理占位页面; Principle_Applied: 模块化页面结构;}}
-->

<template>
  <div class="department-management">
    <a-card title="部门管理" :bordered="false">
      <template #extra>
        <a-button type="primary" :disabled="!canCreate">
          <PlusOutlined />
          新增部门
        </a-button>
      </template>
      
      <a-result
        status="info"
        title="部门管理模块"
        sub-title="此模块将在后续任务中实现，包含部门的树形结构管理、部门信息维护等功能。"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="$router.push('/dashboard')">
              返回首页
            </a-button>
          </a-space>
        </template>
      </a-result>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const canCreate = computed(() => userStore.hasPermission('department:create'))
</script>

<style scoped>
.department-management {
  padding: 24px;
}
</style>
