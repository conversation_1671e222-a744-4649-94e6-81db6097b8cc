<!--
  403权限不足页面
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建403错误页面; Principle_Applied: 错误页面处理;}}
-->

<template>
  <div class="forbidden-page">
    <a-result
      status="403"
      title="403"
      sub-title="抱歉，您没有权限访问此页面。"
    >
      <template #extra>
        <a-space>
          <a-button type="primary" @click="$router.push('/dashboard')">
            返回首页
          </a-button>
          <a-button @click="$router.go(-1)">
            返回上页
          </a-button>
        </a-space>
      </template>
    </a-result>
  </div>
</template>

<script setup lang="ts">
</script>

<style scoped>
.forbidden-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
