<!--
  404页面不存在
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建404错误页面; Principle_Applied: 错误页面处理;}}
-->

<template>
  <div class="not-found-page">
    <a-result
      status="404"
      title="404"
      sub-title="抱歉，您访问的页面不存在。"
    >
      <template #extra>
        <a-space>
          <a-button type="primary" @click="$router.push('/dashboard')">
            返回首页
          </a-button>
          <a-button @click="$router.go(-1)">
            返回上页
          </a-button>
        </a-space>
      </template>
    </a-result>
  </div>
</template>

<script setup lang="ts">
</script>

<style scoped>
.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
