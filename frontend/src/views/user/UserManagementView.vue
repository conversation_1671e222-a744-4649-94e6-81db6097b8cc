<!--
  用户管理页面（占位）
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建用户管理占位页面; Principle_Applied: 模块化页面结构;}}
-->

<template>
  <div class="user-management">
    <a-card title="用户管理" :bordered="false">
      <template #extra>
        <a-button type="primary" :disabled="!canCreate">
          <PlusOutlined />
          新增用户
        </a-button>
      </template>
      
      <a-result
        status="info"
        title="用户管理模块"
        sub-title="此模块将在后续任务中实现，包含用户的增删改查、角色分配等功能。"
      >
        <template #extra>
          <a-space>
            <a-button type="primary" @click="$router.push('/dashboard')">
              返回首页
            </a-button>
          </a-space>
        </template>
      </a-result>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const canCreate = computed(() => userStore.hasPermission('user:create'))
</script>

<style scoped>
.user-management {
  padding: 24px;
}
</style>
