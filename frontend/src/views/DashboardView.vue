<!--
  仪表盘页面
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建仪表盘页面; Principle_Applied: 主页面布局;}}
-->

<template>
  <div class="dashboard">
    <a-row :gutter="24">
      <a-col :span="24">
        <a-card title="欢迎使用刺绣管理系统" :bordered="false">
          <template #extra>
            <a-tag color="blue">{{ userStore.user?.enterprise?.name }}</a-tag>
          </template>
          
          <div class="welcome-content">
            <a-avatar
              :size="64"
              :src="userStore.user?.avatarUrl"
              class="user-avatar"
            >
              {{ userStore.user?.realName?.charAt(0) }}
            </a-avatar>
            
            <div class="user-info">
              <h2>你好，{{ userStore.user?.realName }}！</h2>
              <p>
                <a-tag v-for="role in userStore.roles" :key="role" color="green">
                  {{ formatRoleName(role) }}
                </a-tag>
              </p>
              <p class="last-login">
                最后登录时间：{{ formatDateTime(userStore.user?.lastLoginAt) }}
              </p>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
    
    <a-row :gutter="24" style="margin-top: 24px;">
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card :bordered="false">
          <a-statistic
            title="用户总数"
            :value="statistics.userCount"
            :prefix="h(UserOutlined)"
            :value-style="{ color: '#3f8600' }"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card :bordered="false">
          <a-statistic
            title="部门总数"
            :value="statistics.departmentCount"
            :prefix="h(TeamOutlined)"
            :value-style="{ color: '#cf1322' }"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card :bordered="false">
          <a-statistic
            title="订单总数"
            :value="statistics.orderCount"
            :prefix="h(ShoppingCartOutlined)"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :lg="6">
        <a-card :bordered="false">
          <a-statistic
            title="生产任务"
            :value="statistics.productionCount"
            :prefix="h(ToolOutlined)"
            :value-style="{ color: '#722ed1' }"
          />
        </a-card>
      </a-col>
    </a-row>
    
    <a-row :gutter="24" style="margin-top: 24px;">
      <a-col :span="24">
        <a-card title="快速导航" :bordered="false">
          <div class="quick-nav">
            <a-button
              v-for="nav in quickNavigation"
              :key="nav.path"
              type="primary"
              ghost
              size="large"
              @click="$router.push(nav.path)"
              :disabled="!hasPermission(nav.permission)"
            >
              <component :is="nav.icon" />
              {{ nav.title }}
            </a-button>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, h, onMounted } from 'vue'
import { 
  UserOutlined, 
  TeamOutlined, 
  ShoppingCartOutlined, 
  ToolOutlined,
  UsergroupAddOutlined,
  ApartmentOutlined,
  ShoppingOutlined,
  ScheduleOutlined
} from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'
import { formatRoleName } from '@/utils/auth'
import dayjs from 'dayjs'

const userStore = useUserStore()

// 统计数据
const statistics = ref({
  userCount: 0,
  departmentCount: 0,
  orderCount: 0,
  productionCount: 0
})

// 快速导航
const quickNavigation = [
  {
    title: '用户管理',
    path: '/users',
    icon: UsergroupAddOutlined,
    permission: 'user:view'
  },
  {
    title: '部门管理',
    path: '/departments',
    icon: ApartmentOutlined,
    permission: 'department:view'
  },
  {
    title: '订单管理',
    path: '/orders',
    icon: ShoppingOutlined,
    permission: 'order:view'
  },
  {
    title: '生产计划',
    path: '/production',
    icon: ScheduleOutlined,
    permission: 'production:view'
  }
]

// 检查权限
const hasPermission = (permission: string): boolean => {
  return userStore.hasPermission(permission)
}

// 格式化日期时间
const formatDateTime = (dateTime?: string): string => {
  if (!dateTime) return '暂无'
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 加载统计数据
const loadStatistics = async () => {
  // TODO: 调用API获取统计数据
  // 这里先使用模拟数据
  statistics.value = {
    userCount: 156,
    departmentCount: 12,
    orderCount: 89,
    productionCount: 23
  }
}

onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.welcome-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  background-color: #1890ff;
}

.user-info h2 {
  margin: 0 0 8px 0;
  color: #262626;
}

.user-info p {
  margin: 4px 0;
  color: #8c8c8c;
}

.last-login {
  font-size: 12px;
}

.quick-nav {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.quick-nav .ant-btn {
  height: 60px;
  min-width: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .welcome-content {
    flex-direction: column;
    text-align: center;
  }
  
  .quick-nav {
    justify-content: center;
  }
  
  .quick-nav .ant-btn {
    min-width: 100px;
    height: 50px;
  }
}
</style>
