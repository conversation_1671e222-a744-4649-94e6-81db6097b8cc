/**
 * Vue 3应用入口文件
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-004 前端框架, 创建Vue应用入口; Principle_Applied: 模块化前端架构;}}
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

import App from './App.vue'
import router from './router'
import { setupPermissionDirectives } from './directives/permission'

// 创建Vue应用实例
const app = createApp(App)

// 使用Pinia状态管理
app.use(createPinia())

// 使用Vue Router
app.use(router)

// 使用Ant Design Vue
app.use(Antd)

// 注册权限指令
setupPermissionDirectives(app)

// 挂载应用
app.mount('#app')
