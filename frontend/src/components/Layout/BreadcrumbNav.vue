<!--
  面包屑导航组件
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-005 基础布局, 创建面包屑导航; Principle_Applied: 导航组件化;}}
-->

<template>
  <a-breadcrumb class="breadcrumb-nav">
    <a-breadcrumb-item v-for="(item, index) in breadcrumbItems" :key="item.path || index">
      <router-link v-if="item.path && index < breadcrumbItems.length - 1" :to="item.path">
        <component v-if="item.icon" :is="item.icon" class="breadcrumb-icon" />
        {{ item.title }}
      </router-link>
      <span v-else>
        <component v-if="item.icon" :is="item.icon" class="breadcrumb-icon" />
        {{ item.title }}
      </span>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import {
  DashboardOutlined,
  UsergroupAddOutlined,
  BankOutlined,
  ApartmentOutlined,
  ShoppingOutlined,
  ScheduleOutlined,
  InboxOutlined,
  DollarOutlined,
  CloudOutlined,
  SafetyOutlined
} from '@ant-design/icons-vue'

interface BreadcrumbItem {
  title: string
  path?: string
  icon?: any
}

const route = useRoute()

// 路径图标映射
const pathIconMap: Record<string, any> = {
  '/dashboard': DashboardOutlined,
  '/users': UsergroupAddOutlined,
  '/enterprises': BankOutlined,
  '/departments': ApartmentOutlined,
  '/orders': ShoppingOutlined,
  '/production': ScheduleOutlined,
  '/inventory': InboxOutlined,
  '/salary': DollarOutlined,
  '/digital': CloudOutlined,
  '/roles': SafetyOutlined
}

// 计算面包屑项目
const breadcrumbItems = computed((): BreadcrumbItem[] => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  
  if (matched.length === 0) {
    return []
  }
  
  const breadcrumbs: BreadcrumbItem[] = []
  
  // 如果当前不是首页，添加首页链接
  if (route.path !== '/dashboard') {
    breadcrumbs.push({
      title: '仪表盘',
      path: '/dashboard',
      icon: DashboardOutlined
    })
  }
  
  // 添加当前路由的面包屑
  matched.forEach((item, index) => {
    const isLast = index === matched.length - 1
    const path = isLast ? undefined : item.path
    
    breadcrumbs.push({
      title: item.meta.title as string,
      path,
      icon: pathIconMap[item.path]
    })
  })
  
  return breadcrumbs
})
</script>

<style scoped>
.breadcrumb-nav {
  margin: 0;
}

.breadcrumb-icon {
  margin-right: 4px;
  font-size: 14px;
}

:deep(.ant-breadcrumb-link) {
  display: flex;
  align-items: center;
  color: #8c8c8c;
  transition: color 0.3s;
}

:deep(.ant-breadcrumb-link:hover) {
  color: #1890ff;
}

:deep(.ant-breadcrumb-separator) {
  margin: 0 8px;
  color: #d9d9d9;
}
</style>
