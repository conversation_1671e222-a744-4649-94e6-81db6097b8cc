<!--
  主布局组件
  {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-005 基础布局, 创建主布局组件; Principle_Applied: 布局组件化设计;}}
-->

<template>
  <a-layout class="main-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      :width="240"
      :collapsed-width="80"
      class="layout-sider"
    >
      <!-- 企业信息区域 -->
      <div class="enterprise-info">
        <div class="enterprise-logo">
          <img
            v-if="userStore.user?.enterprise?.logoUrl"
            :src="userStore.user.enterprise.logoUrl"
            :alt="userStore.user.enterprise.name"
            class="logo-img"
          />
          <div v-else class="logo-placeholder">
            <BankOutlined />
          </div>
        </div>
        <div v-if="!collapsed" class="enterprise-name">
          {{ userStore.user?.enterprise?.name || '默认企业' }}
        </div>
      </div>

      <!-- 导航菜单 -->
      <div class="menu-container">
        <SideMenu :collapsed="collapsed" />
      </div>
    </a-layout-sider>

    <!-- 主内容区域 -->
    <a-layout class="layout-content">
      <!-- 顶部导航栏 -->
      <a-layout-header class="layout-header">
        <div class="header-left">
          <a-button
            type="text"
            :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
            @click="toggleCollapsed"
            class="trigger"
          />
          <BreadcrumbNav />
        </div>
        
        <div class="header-right">
          <a-space>
            <!-- 全屏切换 -->
            <a-tooltip title="全屏">
              <a-button
                type="text"
                :icon="h(FullscreenOutlined)"
                @click="toggleFullscreen"
              />
            </a-tooltip>
            
            <!-- 刷新页面 -->
            <a-tooltip title="刷新">
              <a-button
                type="text"
                :icon="h(ReloadOutlined)"
                @click="handleRefresh"
              />
            </a-tooltip>
          </a-space>
        </div>
      </a-layout-header>

      <!-- 页面内容 -->
      <a-layout-content class="page-content">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, h, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Modal, message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BankOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  FullscreenOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import { useUserStore } from '@/stores/user'
import { formatRoleName } from '@/utils/auth'
import SideMenu from './SideMenu.vue'
import BreadcrumbNav from './BreadcrumbNav.vue'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 侧边栏折叠状态
const collapsed = ref(false)

// 移除面包屑相关代码，使用独立的BreadcrumbNav组件

// 切换侧边栏折叠状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}

// 处理用户信息点击
const handleUserProfile = () => {
  if (userStore.hasPermission('user:view')) {
    router.push('/users')
  } else {
    message.info('您没有权限访问用户管理')
  }
}

// 处理系统设置
const handleSettings = () => {
  message.info('系统设置功能将在后续版本中实现')
}

// 处理退出登录
const handleLogout = () => {
  Modal.confirm({
    title: '确认退出',
    content: '您确定要退出登录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      await userStore.logout()
      message.success('已退出登录')
      router.push('/login')
    }
  })
}

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 刷新页面
const handleRefresh = () => {
  window.location.reload()
}

// 监听路由变化，自动收起移动端菜单
watch(route, () => {
  if (window.innerWidth <= 768) {
    collapsed.value = true
  }
})
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
}

.layout-sider {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 100;
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #fff !important;
}

:deep(.ant-layout-sider) {
  background: #fff !important;
}

.enterprise-info {
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.enterprise-logo {
  margin-bottom: 8px;
}

.logo-img {
  width: 40px;
  height: 40px;
  border-radius: 4px;
}

.logo-placeholder {
  width: 40px;
  height: 40px;
  background: #1890ff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin: 0 auto;
}

.enterprise-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menu-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;
}



.layout-content {
  margin-left: 240px;
  transition: margin-left 0.2s;
}

.layout-content.collapsed {
  margin-left: 80px;
}

.layout-header {
  background: #fff;
  padding: 0 24px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 99;
}

.header-left {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  margin-right: 16px;
}

.breadcrumb {
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.page-content {
  min-height: calc(100vh - 64px);
  background: #f0f2f5;
}

/* 页面切换动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-sider {
    transform: translateX(-100%);
    transition: transform 0.3s;
  }
  
  .layout-sider:not(.ant-layout-sider-collapsed) {
    transform: translateX(0);
  }
  
  .layout-content {
    margin-left: 0 !important;
  }
  
  .enterprise-name,
  .user-details {
    display: none;
  }
}
</style>
